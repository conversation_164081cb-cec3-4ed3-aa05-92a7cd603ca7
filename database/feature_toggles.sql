-- Create feature_toggles table for managing feature access
CREATE TABLE IF NOT EXISTS feature_toggles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    feature_key VARCHAR(100) UNIQUE NOT NULL,
    feature_name VARCHAR(200) NOT NULL,
    description TEXT,
    enabled BOOLEAN DEFAULT true,
    role_restrictions TEXT[], -- Array of roles that can access this feature
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_feature_toggles_key ON feature_toggles(feature_key);
CREATE INDEX IF NOT EXISTS idx_feature_toggles_enabled ON feature_toggles(enabled);

-- Insert default feature toggles
INSERT INTO feature_toggles (feature_key, feature_name, description, enabled, role_restrictions) VALUES
('store_management', 'Store Management', 'Access to store creation and management', true, ARRAY['admin']),
('product_management', 'Product Management', 'Access to product creation and management', true, ARRAY['admin', 'store_owner']),
('order_management', 'Order Management', 'Access to order processing and management', true, ARRAY['admin', 'store_owner']),
('payout_management', 'Payout Management', 'Access to payout processing and management', true, ARRAY['admin', 'store_owner']),
('analytics_view', 'Analytics View', 'Access to analytics and reporting', true, ARRAY['admin', 'store_owner']),
('user_management', 'User Management', 'Access to user management', true, ARRAY['admin']),
('synergy_store', 'Synergy Store', 'Access to synergy store management', true, ARRAY['admin']),
('settings_access', 'Settings Access', 'Access to system settings', true, ARRAY['admin', 'store_owner'])
ON CONFLICT (feature_key) DO UPDATE SET
    feature_name = EXCLUDED.feature_name,
    description = EXCLUDED.description,
    enabled = EXCLUDED.enabled,
    role_restrictions = EXCLUDED.role_restrictions,
    updated_at = NOW();

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_feature_toggles_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update updated_at
DROP TRIGGER IF EXISTS trigger_update_feature_toggles_updated_at ON feature_toggles;
CREATE TRIGGER trigger_update_feature_toggles_updated_at
    BEFORE UPDATE ON feature_toggles
    FOR EACH ROW
    EXECUTE FUNCTION update_feature_toggles_updated_at();

-- Enable RLS (Row Level Security) if needed
ALTER TABLE feature_toggles ENABLE ROW LEVEL SECURITY;

-- Create policy for authenticated users to read feature toggles
CREATE POLICY "Allow authenticated users to read feature toggles" ON feature_toggles
    FOR SELECT USING (auth.role() = 'authenticated');

-- Create policy for admins to manage feature toggles
CREATE POLICY "Allow admins to manage feature toggles" ON feature_toggles
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'admin'
        )
    );

-- Grant necessary permissions
GRANT SELECT ON feature_toggles TO authenticated;
GRANT ALL ON feature_toggles TO service_role;
