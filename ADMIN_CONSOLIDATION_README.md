# Admin Interface Consolidation

This document outlines the changes made to consolidate the store-admin functionality into a single admin interface with feature toggles.

## Overview

The previous system had separate admin interfaces:
- `/admin` - For super admins
- `/store-admin` - For store owners

The new system has a unified admin interface at `/admin` that both admins and store owners can access, with feature toggles controlling what each role can see and do.

## Key Changes

### 1. Removed Files
- **Entire `/app/store-admin/` directory** - All store-admin pages and layouts
- **`/components/store-admin/StoreAdminLayout.tsx`** - Store admin layout component
- **`/features/store-admin/api.ts`** - Store admin API functions

### 2. Updated Role Permissions
**File: `/lib/types/roles.ts`**
- Removed `canAccessStoreAdmin` permission
- Added `canManageFeatureToggles` permission for admins
- Updated `store_owner` role to have `canAccessAdmin: true`
- Added feature toggle types: `FeatureToggle`, `FeatureKey`

### 3. Feature Toggle System
**New File: `/lib/services/feature-toggles.ts`**
- `FeatureToggleService` class for managing feature access
- React hook `useFeatureToggle` for components
- Default feature configurations for different roles

**New Database Table: `feature_toggles`**
- Stores feature toggle configurations
- Supports role-based restrictions
- Real-time updates via Supabase

### 4. Updated Admin Layout
**File: `/components/admin/AdminLayout.tsx`**
- Added feature toggle initialization
- Dynamic navigation based on enabled features
- Role-aware feature filtering

### 5. Updated Navigation Components
**Files Updated:**
- `/components/AuthAwareButtons.tsx` - Consolidated admin links
- `/components/ecommerce/Header.tsx` - Single admin panel link
- `/lib/supabase/middleware.ts` - Removed store-admin route protection

### 6. New Admin Settings Page
**New File: `/app/admin/settings/page.tsx`**
- Feature toggle management interface
- Role-based access control
- Real-time toggle updates

### 7. Role-Based Product Management
**File: `/app/admin/products/page.tsx`**
- Store owners see only their products
- Admins see all products
- Role-aware descriptions and filtering

## Feature Toggles

The system includes the following feature toggles:

| Feature Key | Description | Default Roles |
|-------------|-------------|---------------|
| `store_management` | Store creation and management | admin |
| `product_management` | Product creation and management | admin, store_owner |
| `order_management` | Order processing and management | admin, store_owner |
| `payout_management` | Payout processing and management | admin, store_owner |
| `analytics_view` | Analytics and reporting access | admin, store_owner |
| `user_management` | User management access | admin |
| `synergy_store` | Synergy store management | admin |
| `settings_access` | System settings access | admin, store_owner |

## Database Setup

Run the following SQL script to set up the feature toggles table:

```sql
-- See database/feature_toggles.sql for complete setup
```

## Usage

### For Admins
1. Access `/admin` interface
2. See all features and pages
3. Manage feature toggles in Settings
4. Control what store owners can access

### For Store Owners
1. Access `/admin` interface (same as admins)
2. See only features enabled by admin
3. View only their own store data
4. Cannot access feature toggle management

### Feature Toggle Management
1. Go to `/admin/settings`
2. Toggle features on/off for different roles
3. Changes take effect immediately
4. Role restrictions control access

## Migration Notes

### For Existing Store Owners
- Previous `/store-admin` URLs will no longer work
- Users should be redirected to `/admin`
- All functionality is preserved but consolidated

### For Existing Admins
- All previous functionality remains
- New feature toggle management capabilities
- Can control store owner access granularly

## Benefits

1. **Simplified Architecture** - Single admin interface to maintain
2. **Flexible Access Control** - Granular feature-level permissions
3. **Better User Experience** - Consistent interface for all admin users
4. **Easier Maintenance** - One codebase instead of two separate admin systems
5. **Scalable Permissions** - Easy to add new features and roles

## Technical Implementation

### Feature Toggle Service
```typescript
// Check if user can access a feature
const canAccess = featureToggleService.isFeatureEnabled('product_management', userRole);

// Update feature toggle (admin only)
await featureToggleService.updateFeatureToggle('product_management', true);
```

### React Hook Usage
```typescript
const { isEnabled, loading } = useFeatureToggle('product_management', userRole);
```

### Navigation Filtering
```typescript
const navigation = allNavigationItems.filter(item => 
  enabledFeatures.has(item.feature)
);
```

## Security Considerations

1. **Database-Level Security** - RLS policies protect feature toggle access
2. **Role-Based Access** - Server-side validation of permissions
3. **Real-Time Updates** - Changes propagate immediately to all users
4. **Audit Trail** - Feature toggle changes are logged with timestamps

## Future Enhancements

1. **User-Level Toggles** - Individual user feature access
2. **Time-Based Toggles** - Features that enable/disable automatically
3. **A/B Testing** - Percentage-based feature rollouts
4. **Feature Dependencies** - Features that require other features
5. **Audit Logging** - Detailed logs of who changed what and when
