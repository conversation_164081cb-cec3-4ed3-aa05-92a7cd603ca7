-- Comprehensive fix for database issues
-- This script addresses three main issues:
-- 1. Missing 'options' column in the cart table
-- 2. Type mismatch between cart.product_id and products.id (UUID vs bigint)
-- 3. NOT NULL constraint violation when product_id is NULL

-- 1. Fix missing options column in cart table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'cart' AND column_name = 'options'
    ) THEN
        -- Add options column to cart table if it doesn't exist
        ALTER TABLE cart ADD COLUMN options JSONB;
        RAISE NOTICE 'Added options column to cart table';
    ELSE
        RAISE NOTICE 'options column already exists in cart table';
    END IF;
END $$;

-- 2. Check for type mismatch between cart.product_id and products.id
DO $$
DECLARE
    cart_type TEXT;
    products_type TEXT;
BEGIN
    -- Get the data type of cart.product_id
    SELECT data_type INTO cart_type
    FROM information_schema.columns
    WHERE table_name = 'cart' AND column_name = 'product_id';

    -- Get the data type of products.id
    SELECT data_type INTO products_type
    FROM information_schema.columns
    WHERE table_name = 'products' AND column_name = 'id';

    -- Output the types
    RAISE NOTICE 'cart.product_id type: %, products.id type: %', cart_type, products_type;

    -- Check if there's a mismatch that needs fixing
    IF cart_type = 'bigint' AND products_type = 'uuid' THEN
        RAISE NOTICE 'Type mismatch detected. Will attempt to fix cart.product_id from bigint to UUID';

        -- Create a backup of the cart table
        CREATE TABLE cart_backup AS SELECT * FROM cart;
        RAISE NOTICE 'Created backup table cart_backup';

        -- Drop foreign key constraint
        ALTER TABLE cart DROP CONSTRAINT IF EXISTS cart_product_id_fkey;

        -- Drop unique constraint that includes product_id
        ALTER TABLE cart DROP CONSTRAINT IF EXISTS cart_user_product_key;
        DROP INDEX IF EXISTS cart_user_product_key;

        -- Temporarily remove NOT NULL constraint to allow the conversion
        ALTER TABLE cart ALTER COLUMN product_id DROP NOT NULL;
        RAISE NOTICE 'Temporarily removed NOT NULL constraint from product_id column';

        -- Alter the column type
        -- Note: This will only work if the data can be converted
        BEGIN
            -- First attempt: Try to convert existing data
            ALTER TABLE cart
            ALTER COLUMN product_id TYPE uuid
            USING NULL; -- Set all to NULL initially to avoid conversion errors

            RAISE NOTICE 'Changed product_id column type to UUID. All values set to NULL temporarily.';
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Error converting column: %', SQLERRM;
            RAISE NOTICE 'Will try alternative approach';

            -- If conversion fails, recreate the column
            ALTER TABLE cart DROP COLUMN product_id;
            ALTER TABLE cart ADD COLUMN product_id uuid;
            RAISE NOTICE 'Recreated product_id column as UUID type with all NULL values';
        END;

        -- Delete cart entries with NULL product_id
        DELETE FROM cart WHERE product_id IS NULL;
        RAISE NOTICE 'Deleted cart entries with NULL product_id';

        -- Add back the NOT NULL constraint
        ALTER TABLE cart ALTER COLUMN product_id SET NOT NULL;
        RAISE NOTICE 'Added back NOT NULL constraint to product_id column';

        -- Recreate the unique constraint
        ALTER TABLE cart ADD CONSTRAINT cart_user_product_key UNIQUE (user_id, product_id);

        -- Recreate the foreign key constraint
        ALTER TABLE cart ADD CONSTRAINT cart_product_id_fkey
        FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE;

        RAISE NOTICE 'Fixed cart table schema. Cart entries with NULL product_id have been removed.';
    ELSIF cart_type <> products_type THEN
        RAISE NOTICE 'Type mismatch detected between cart.product_id (%) and products.id (%). Manual intervention required.', cart_type, products_type;
    ELSE
        RAISE NOTICE 'Types match between cart.product_id and products.id. No fix needed.';
    END IF;
END $$;

-- 3. Clean up any cart entries with invalid product_id values
DELETE FROM cart WHERE product_id::text = 'NaN' OR product_id IS NULL;
RAISE NOTICE 'Cleaned up any remaining cart entries with invalid product_id values';

-- 4. Display current cart schema for verification
SELECT column_name, data_type, column_default, is_nullable
FROM information_schema.columns
WHERE table_name = 'cart'
ORDER BY ordinal_position;

-- Note: After running this script, you should also update your TypeScript code
-- to handle both UUID and number types for product IDs. See the fix_code.sh script
-- for automated fixes to your TypeScript code.
