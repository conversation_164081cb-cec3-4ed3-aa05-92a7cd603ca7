-- Update the products table to set a default inventory quantity for all products
-- This will ensure that all products are shown as in stock

-- First, update the default value for new products
ALTER TABLE products ALTER COLUMN inventory_quantity SET DEFAULT 100;

-- Then, update all existing products with 0 inventory to have 100 items
UPDATE products SET inventory_quantity = 100 WHERE inventory_quantity = 0;

-- Add a comment to explain the change
COMMENT ON COLUMN products.inventory_quantity IS 'Inventory quantity. Default is 100 to ensure products are in stock by default.';
