-- Debug and fix all RLS policies
-- This script will completely reset and fix all policies

BEGIN;

-- 1. First, let's see what policies exist (for debugging)
-- You can run this query separately to see current policies:
-- SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
-- FROM pg_policies 
-- WHERE schemaname = 'public' 
-- ORDER BY tablename, policyname;

-- 2. Drop ALL existing policies to start fresh
-- Stores policies
DROP POLICY IF EXISTS "Anyone can view active stores" ON stores;
DROP POLICY IF EXISTS "Store owners can view their stores" ON stores;
DROP POLICY IF EXISTS "Store owners can update their stores" ON stores;
DROP POLICY IF EXISTS "Admins can insert stores" ON stores;
DROP POLICY IF EXISTS "Public can view all stores" ON stores;

-- Products policies  
DROP POLICY IF EXISTS "Anyone can view products" ON products;
DROP POLICY IF EXISTS "Store owners can manage their products" ON products;
DROP POLICY IF EXISTS "Public can view all products" ON products;

-- Categories policies
DROP POLICY IF EXISTS "Anyone can view categories" ON categories;
DROP POLICY IF EXISTS "Public can view categories" ON categories;
DROP POLICY IF EXISTS "Admins can manage categories" ON categories;

-- Notifications policies
DROP POLICY IF EXISTS "Users can view their notifications" ON notifications;
DROP POLICY IF EXISTS "Users can update their notifications" ON notifications;
DROP POLICY IF EXISTS "System can insert notifications" ON notifications;

-- Orders policies
DROP POLICY IF EXISTS "Users can view their orders" ON orders;
DROP POLICY IF EXISTS "Users can create their orders" ON orders;
DROP POLICY IF EXISTS "Admins can update orders" ON orders;
DROP POLICY IF EXISTS "Users can view their own orders" ON orders;

-- 3. Temporarily disable RLS to ensure data access
ALTER TABLE stores DISABLE ROW LEVEL SECURITY;
ALTER TABLE products DISABLE ROW LEVEL SECURITY;
ALTER TABLE categories DISABLE ROW LEVEL SECURITY;

-- 4. Re-enable RLS and create simple, permissive policies
ALTER TABLE stores ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;

-- 5. Create very simple policies that allow public access
-- Stores - allow everyone to view all stores
CREATE POLICY "allow_all_stores_select" ON stores
  FOR SELECT USING (true);

-- Products - allow everyone to view all products  
CREATE POLICY "allow_all_products_select" ON products
  FOR SELECT USING (true);

-- Categories - allow everyone to view all categories
CREATE POLICY "allow_all_categories_select" ON categories
  FOR SELECT USING (true);

-- 6. Add authenticated user policies for data modification
-- Store owners can manage their stores
CREATE POLICY "store_owners_manage_stores" ON stores
  FOR ALL TO authenticated
  USING (
    owner_id = auth.uid() OR
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin')
  );

-- Store owners can manage their products
CREATE POLICY "store_owners_manage_products" ON products
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM stores 
      WHERE id = products.store_id AND owner_id = auth.uid()
    ) OR
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin')
  );

-- Admins can manage categories
CREATE POLICY "admins_manage_categories" ON categories
  FOR ALL TO authenticated
  USING (
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin')
  );

-- 7. Handle notifications separately (keep existing structure)
CREATE POLICY "users_view_own_notifications" ON notifications
  FOR SELECT TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "users_update_own_notifications" ON notifications
  FOR UPDATE TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "system_insert_notifications" ON notifications
  FOR INSERT TO authenticated
  WITH CHECK (true);

-- 8. Handle orders
CREATE POLICY "users_view_own_orders" ON orders
  FOR SELECT TO authenticated
  USING (
    user_id = auth.uid() OR
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin')
  );

CREATE POLICY "users_create_orders" ON orders
  FOR INSERT TO authenticated
  WITH CHECK (user_id = auth.uid());

CREATE POLICY "admins_update_orders" ON orders
  FOR UPDATE TO authenticated
  USING (
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin')
  );

-- 9. Handle other tables if they exist
DO $$
BEGIN
  -- Product images
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'product_images') THEN
    DROP POLICY IF EXISTS "Public can view product images" ON product_images;
    DROP POLICY IF EXISTS "Store owners can manage product images" ON product_images;
    
    CREATE POLICY "allow_all_product_images_select" ON product_images
      FOR SELECT USING (true);
      
    CREATE POLICY "store_owners_manage_product_images" ON product_images
      FOR ALL TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM products p
          JOIN stores s ON p.store_id = s.id
          WHERE p.id = product_images.product_id AND s.owner_id = auth.uid()
        ) OR
        EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin')
      );
  END IF;

  -- Order items
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'order_items') THEN
    DROP POLICY IF EXISTS "Users can view their order items" ON order_items;
    DROP POLICY IF EXISTS "Store owners can view their order items" ON order_items;
    DROP POLICY IF EXISTS "Users can insert their order items" ON order_items;
    
    CREATE POLICY "users_view_order_items" ON order_items
      FOR SELECT TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM orders 
          WHERE id = order_items.order_id AND user_id = auth.uid()
        ) OR
        EXISTS (
          SELECT 1 FROM stores 
          WHERE id = order_items.store_id AND owner_id = auth.uid()
        ) OR
        EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin')
      );
      
    CREATE POLICY "users_insert_order_items" ON order_items
      FOR INSERT TO authenticated
      WITH CHECK (
        EXISTS (
          SELECT 1 FROM orders 
          WHERE id = order_items.order_id AND user_id = auth.uid()
        )
      );
  END IF;

  -- Order store items
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'order_store_items') THEN
    DROP POLICY IF EXISTS "Store owners can view their order items" ON order_store_items;
    DROP POLICY IF EXISTS "Store owners can update their order items" ON order_store_items;
    
    CREATE POLICY "store_owners_view_order_store_items" ON order_store_items
      FOR SELECT TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM stores 
          WHERE id = order_store_items.store_id AND owner_id = auth.uid()
        ) OR
        EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin')
      );
      
    CREATE POLICY "store_owners_update_order_store_items" ON order_store_items
      FOR UPDATE TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM stores 
          WHERE id = order_store_items.store_id AND owner_id = auth.uid()
        ) OR
        EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin')
      );
  END IF;
END
$$;

COMMIT;

-- Show final policies for verification
SELECT schemaname, tablename, policyname, permissive, cmd, qual 
FROM pg_policies 
WHERE schemaname = 'public' AND tablename IN ('stores', 'products', 'categories', 'notifications', 'orders')
ORDER BY tablename, policyname;
