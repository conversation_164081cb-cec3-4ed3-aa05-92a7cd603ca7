-- Set up user roles correctly
-- <EMAIL> = admin
-- <EMAIL> = store_owner

BEGIN;

-- Update <EMAIL> to admin
UPDATE profiles 
SET role = 'admin' 
WHERE email = '<EMAIL>';

-- Update <EMAIL> to store_owner  
UPDATE profiles 
SET role = 'store_owner' 
WHERE email = '<EMAIL>';

-- Assign <EMAIL> as owner of the store if they have a profile
UPDATE stores 
SET owner_id = (
  SELECT id FROM profiles WHERE email = '<EMAIL>' LIMIT 1
)
WHERE id = 'ec0f0876-136d-4913-8ccd-14847ba516f1'
AND EXISTS (SELECT 1 FROM profiles WHERE email = '<EMAIL>');

-- Verify the changes
SELECT 
  p.email, 
  p.role, 
  p.id,
  s.name as store_name,
  s.id as store_id
FROM profiles p
LEFT JOIN stores s ON s.owner_id = p.id
WHERE p.email IN ('<EMAIL>', '<EMAIL>')
ORDER BY p.email;

COMMIT;
