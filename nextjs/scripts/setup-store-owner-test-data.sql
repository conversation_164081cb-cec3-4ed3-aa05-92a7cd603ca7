-- Setup test data for store owner testing
-- Run this script in Supabase SQL editor

BEGIN;

-- 1. Ensure <EMAIL> has store_owner role
UPDATE profiles 
SET role = 'store_owner' 
WHERE email = '<EMAIL>';

-- 2. Get the user <NAME_EMAIL>
DO $$
DECLARE
    store_owner_id UUID;
    test_store_id UUID;
    test_product_id UUID;
    test_order_id UUID;
    test_user_id UUID;
BEGIN
    -- Get store owner user ID
    SELECT id INTO store_owner_id FROM profiles WHERE email = '<EMAIL>';
    
    IF store_owner_id IS NULL THEN
        RAISE EXCEPTION '<NAME_EMAIL> not found in profiles';
    END IF;
    
    -- Create a test store for the store owner if it doesn't exist
    INSERT INTO stores (id, name, slug, description, owner_id, contact_email, created_at, updated_at)
    VALUES (
        uuid_generate_v4(),
        'Test Store for Find',
        'test-store-find',
        'A test store for the <NAME_EMAIL>',
        store_owner_id,
        '<EMAIL>',
        NOW(),
        NOW()
    )
    ON CONFLICT (slug) DO UPDATE SET
        owner_id = store_owner_id,
        updated_at = NOW()
    RETURNING id INTO test_store_id;
    
    -- Get the store ID if it already existed
    IF test_store_id IS NULL THEN
        SELECT id INTO test_store_id FROM stores WHERE slug = 'test-store-find';
    END IF;
    
    -- Create a test product in this store if it doesn't exist
    INSERT INTO products (id, name, slug, description, price, currency, store_id, created_at, updated_at)
    VALUES (
        uuid_generate_v4(),
        'Test Product',
        'test-product-find',
        'A test product for testing store owner orders',
        100.00,
        'GMD',
        test_store_id,
        NOW(),
        NOW()
    )
    ON CONFLICT (slug) DO UPDATE SET
        store_id = test_store_id,
        updated_at = NOW()
    RETURNING id INTO test_product_id;
    
    -- Get the product ID if it already existed
    IF test_product_id IS NULL THEN
        SELECT id INTO test_product_id FROM products WHERE slug = 'test-product-find';
    END IF;
    
    -- Get a test user (<NAME_EMAIL> as customer)
    SELECT id INTO test_user_id FROM profiles WHERE email = '<EMAIL>';
    
    IF test_user_id IS NULL THEN
        -- Use the store owner as customer for testing
        test_user_id := store_owner_id;
    END IF;
    
    -- Create a test order if none exists
    INSERT INTO orders (id, user_id, status, total, currency, shipping_name, shipping_email, shipping_phone, created_at, updated_at)
    VALUES (
        uuid_generate_v4(),
        test_user_id,
        'pending',
        100.00,
        'GMD',
        'Test Customer',
        '<EMAIL>',
        '+2207777777',
        NOW(),
        NOW()
    )
    RETURNING id INTO test_order_id;
    
    -- Create order item linking the order to the store's product
    INSERT INTO order_items (id, order_id, product_id, store_id, quantity, price, total, currency, created_at)
    VALUES (
        uuid_generate_v4(),
        test_order_id,
        test_product_id,
        test_store_id,
        1,
        100.00,
        100.00,
        'GMD',
        NOW()
    );
    
    RAISE NOTICE 'Test data created successfully:';
    RAISE NOTICE 'Store Owner ID: %', store_owner_id;
    RAISE NOTICE 'Test Store ID: %', test_store_id;
    RAISE NOTICE 'Test Product ID: %', test_product_id;
    RAISE NOTICE 'Test Order ID: %', test_order_id;
    
END $$;

COMMIT;

-- Verify the setup
SELECT 'VERIFICATION:' as section;
SELECT 
    p.email as store_owner_email,
    s.name as store_name,
    pr.name as product_name,
    o.id as order_id,
    o.status as order_status,
    o.total as order_total
FROM profiles p
JOIN stores s ON p.id = s.owner_id
JOIN products pr ON s.id = pr.store_id
JOIN order_items oi ON pr.id = oi.product_id AND s.id = oi.store_id
JOIN orders o ON oi.order_id = o.id
WHERE p.email = '<EMAIL>';
