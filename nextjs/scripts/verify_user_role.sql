-- Verify user role assignment
-- Run this to check if the user role was set correctly

-- Check the user's profile and role
SELECT 
  id,
  email,
  role,
  first_name,
  last_name,
  created_at
FROM profiles 
WHERE id = '8933b3d6-96de-400c-8643-d1d2ba90b34c';

-- Check the store ownership
SELECT 
  s.id as store_id,
  s.name as store_name,
  s.owner_id,
  p.email as owner_email,
  p.role as owner_role
FROM stores s
JOIN profiles p ON s.owner_id = p.id
WHERE s.id = 'ec0f0876-136d-4913-8ccd-14847ba516f1';

-- Check if there are any auth users without profiles
SELECT 
  au.id,
  au.email,
  au.created_at as auth_created,
  p.id as profile_id,
  p.role
FROM auth.users au
LEFT JOIN profiles p ON au.id = p.id
WHERE au.id = '8933b3d6-96de-400c-8643-d1d2ba90b34c';

-- Show all current policies on profiles table
SELECT 
  schemaname, 
  tablename, 
  policyname, 
  permissive, 
  roles, 
  cmd, 
  qual 
FROM pg_policies 
WHERE tablename = 'profiles'
ORDER BY policyname;
