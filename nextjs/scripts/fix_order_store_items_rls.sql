-- Fix RLS policies for order_store_items table
-- This script adds the missing INSERT policy for regular users

BEGIN;

-- Check if order_store_items table exists
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'order_store_items') THEN
    -- Drop existing policies to start fresh
    DROP POLICY IF EXISTS "order_store_items_access" ON order_store_items;
    DROP POLICY IF EXISTS "Store owners can view their order items" ON order_store_items;
    DROP POLICY IF EXISTS "Store owners can update their order items" ON order_store_items;
    DROP POLICY IF EXISTS "store_owners_view_order_store_items" ON order_store_items;
    DROP POLICY IF EXISTS "store_owners_update_order_store_items" ON order_store_items;
    DROP POLICY IF EXISTS "store_owners_view_order_store_items_simple" ON order_store_items;
    DROP POLICY IF EXISTS "store_owners_update_order_store_items_simple" ON order_store_items;
    
    -- Create comprehensive policies for order_store_items
    
    -- Allow authenticated users to insert order_store_items (needed for checkout)
    CREATE POLICY "authenticated_users_can_insert_order_store_items" ON order_store_items
      FOR INSERT TO authenticated
      WITH CHECK (true);
    
    -- Allow users to view order_store_items for their own orders
    CREATE POLICY "users_can_view_their_order_store_items" ON order_store_items
      FOR SELECT TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM orders 
          WHERE id = order_store_items.order_id AND user_id = auth.uid()
        ) OR
        EXISTS (
          SELECT 1 FROM stores 
          WHERE id = order_store_items.store_id AND owner_id = auth.uid()
        ) OR
        auth.email() = '<EMAIL>'
      );
    
    -- Allow store owners and admins to update order_store_items
    CREATE POLICY "store_owners_can_update_order_store_items" ON order_store_items
      FOR UPDATE TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM stores 
          WHERE id = order_store_items.store_id AND owner_id = auth.uid()
        ) OR
        auth.email() = '<EMAIL>'
      );
    
    RAISE NOTICE 'Updated RLS policies for order_store_items table';
  ELSE
    RAISE NOTICE 'order_store_items table does not exist';
  END IF;
END $$;

-- Also check if the trigger function exists and is working properly
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.routines WHERE routine_name = 'create_order_store_items') THEN
    RAISE NOTICE 'create_order_store_items trigger function exists';
  ELSE
    RAISE NOTICE 'create_order_store_items trigger function does not exist - creating it';
    
    -- Create the trigger function
    CREATE OR REPLACE FUNCTION create_order_store_items()
    RETURNS TRIGGER AS $func$
    BEGIN
      -- Insert or update order_store_items for this store
      INSERT INTO order_store_items (order_id, store_id, total_amount, currency)
      VALUES (NEW.order_id, NEW.store_id, NEW.total, NEW.currency)
      ON CONFLICT (order_id, store_id)
      DO UPDATE SET
        total_amount = order_store_items.total_amount + NEW.total,
        updated_at = NOW();

      RETURN NEW;
    END;
    $func$ LANGUAGE plpgsql;
    
    -- Create the trigger if it doesn't exist
    DROP TRIGGER IF EXISTS trigger_create_order_store_items ON order_items;
    CREATE TRIGGER trigger_create_order_store_items
      AFTER INSERT ON order_items
      FOR EACH ROW
      EXECUTE FUNCTION create_order_store_items();
      
    RAISE NOTICE 'Created trigger function and trigger for order_store_items';
  END IF;
END $$;

COMMIT;

-- Show the current policies for verification
SELECT schemaname, tablename, policyname, permissive, cmd, qual 
FROM pg_policies 
WHERE schemaname = 'public' AND tablename = 'order_store_items'
ORDER BY policyname;
