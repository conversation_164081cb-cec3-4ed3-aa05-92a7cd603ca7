# TypeScript Code Fixes for Database Issues

## Problem Analysis

1. **Schema Mismatch**: There are two different database schemas in the codebase:
   - `finder_schema.sql`: Uses UUID for product IDs and cart.product_id
   - `20250201000000_ecommerce_schema.sql`: Uses bigint for product IDs and cart.product_id

2. **Type Conversion Issues**: The code is using `Number()` to convert product IDs, which can result in "NaN" values when trying to convert UUID strings to numbers.

## Required Code Changes

The following files need to be updated to fix the UUID conversion issues:

1. `nextjs/src/lib/services/ecommerce.ts`
2. `nextjs/src/lib/services/ecommerce-client.ts`
3. `nextjs/src/features/products/components/ProductDetails.tsx`
4. `nextjs/src/features/products/components/ProductCard.tsx`

## Recommended Approach

1. **Update the database schema** using the SQL script we've created
2. **Update the TypeScript code** to handle both UUID and number types for product IDs
3. **Update the type definitions** to reflect the correct types

## Implementation Steps

1. Run the `fix_database_issues.sql` script to fix the database schema
2. Update the TypeScript code to handle UUID product IDs
3. Test the cart functionality to ensure it works correctly

## Code Changes

For each file that uses `Number(id)` when adding to cart, replace with:

```typescript
// Before
await EcommerceClientService.addToCart(Number(id), quantity);

// After
await EcommerceClientService.addToCart(id, quantity);
```

And update the service methods to handle both UUID and number types:

```typescript
// Before
static async addToCart(productId: number, quantity = 1, options: Record<string, any> | null = null): Promise<void> {
  // ...
}

// After
static async addToCart(productId: string | number, quantity = 1, options: Record<string, any> | null = null): Promise<void> {
  // ...
}
```
