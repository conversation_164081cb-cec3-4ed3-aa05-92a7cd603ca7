-- Fix cart table schema issues

-- Check if options column exists in cart table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'cart' AND column_name = 'options'
    ) THEN
        -- Add options column to cart table if it doesn't exist
        ALTER TABLE cart ADD COLUMN options JSONB;
        RAISE NOTICE 'Added options column to cart table';
    ELSE
        RAISE NOTICE 'options column already exists in cart table';
    END IF;
END $$;

-- Check product_id column type in cart table
DO $$
DECLARE
    column_type TEXT;
BEGIN
    SELECT data_type INTO column_type
    FROM information_schema.columns
    WHERE table_name = 'cart' AND column_name = 'product_id';
    
    RAISE NOTICE 'Current product_id column type: %', column_type;
    
    -- If product_id is bigint but products.id is UUID, we need to fix this mismatch
    IF column_type = 'bigint' THEN
        RAISE NOTICE 'Warning: product_id in cart is bigint but products.id might be UUID. This could cause type mismatch errors.';
        -- Note: Converting column types with existing data requires careful migration
    END IF;
END $$;

-- Display current cart schema for verification
SELECT column_name, data_type, column_default, is_nullable
FROM information_schema.columns
WHERE table_name = 'cart'
ORDER BY ordinal_position;
