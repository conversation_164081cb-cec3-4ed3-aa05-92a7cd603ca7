# Database and Code Fix Scripts

This directory contains scripts to fix database schema and code issues related to the cart functionality.

## Issues Addressed

1. **Missing 'options' column in cart table**
   - Error: `"Could not find the 'options' column of 'cart' in the schema cache"`
   - The code expects an 'options' column in the cart table, but it doesn't exist in the current schema.

2. **UUID conversion errors**
   - Error: `"invalid input syntax for type uuid: \"NaN\""`
   - This happens because the code is trying to convert product IDs to numbers using `Number()`, but the database is using UUIDs.

3. **NULL value constraint violation**
   - Error: `"null value in column \"product_id\" of relation \"cart\" violates not-null constraint"`
   - This occurs when the product_id column is set to NULL during the type conversion process.

## Fix Scripts

### 1. Database Schema Fix

Run the following SQL script to fix the database schema:

```bash
# Connect to your Supabase database and run:
psql -f nextjs/scripts/fix_database_issues.sql
```

This script will:
- Add the missing 'options' column to the cart table if it doesn't exist
- Check for type mismatches between cart.product_id and products.id
- Fix any type mismatches by converting the column type
- Handle the NOT NULL constraint properly during conversion
- Clean up any invalid cart entries

### 2. TypeScript Code Fix

Run the following script to fix the TypeScript code:

```bash
# Navigate to the nextjs directory and run:
cd nextjs
./scripts/fix_code.sh
```

This script will update:
- Type definitions in service methods to accept both string and number IDs
- Remove `Number()` conversions when adding items to cart

## Verification

After running both fixes, you should:

1. Verify the cart table schema:
```sql
SELECT column_name, data_type, column_default, is_nullable
FROM information_schema.columns
WHERE table_name = 'cart'
ORDER BY ordinal_position;
```

2. Test the cart functionality in the application to ensure it works correctly.

## Additional Information

- The database schema has two different versions:
  - `finder_schema.sql`: Uses UUID for product IDs
  - `20250201000000_ecommerce_schema.sql`: Uses bigint for product IDs

- The fix scripts ensure compatibility with the UUID-based schema, which appears to be the one currently in use.

- If you encounter any issues after applying these fixes, you may need to check for other code that assumes product IDs are numbers rather than UUIDs.
