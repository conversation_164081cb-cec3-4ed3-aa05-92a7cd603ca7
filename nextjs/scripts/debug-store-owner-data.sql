-- Debug script to check store owner data
-- Run this in Supabase SQL editor to see what data exists

-- 1. Check profiles and their roles
SELECT 'PROFILES:' as section;
SELECT id, email, role, first_name, last_name, created_at 
FROM profiles 
WHERE email IN ('<EMAIL>', '<EMAIL>')
ORDER BY email;

-- 2. Check stores and their owners
SELECT 'STORES:' as section;
SELECT s.id, s.name, s.owner_id, p.email as owner_email
FROM stores s
LEFT JOIN profiles p ON s.owner_id = p.id
ORDER BY s.created_at DESC
LIMIT 10;

-- 3. Check orders
SELECT 'ORDERS:' as section;
SELECT id, user_id, status, total, currency, created_at
FROM orders
ORDER BY created_at DESC
LIMIT 10;

-- 4. Check order_items and their store relationships
SELECT 'ORDER_ITEMS:' as section;
SELECT oi.id, oi.order_id, oi.product_id, oi.store_id, oi.quantity, oi.price, oi.total,
       s.name as store_name, p.name as product_name
FROM order_items oi
LEFT JOIN stores s ON oi.store_id = s.id
LEFT JOIN products p ON oi.product_id = p.id
ORDER BY oi.created_at DESC
LIMIT 10;

-- 5. <NAME_EMAIL> has any stores
SELECT '<EMAIL> STORES:' as section;
SELECT s.id, s.name, s.owner_id, p.email
FROM stores s
JOIN profiles p ON s.owner_id = p.id
WHERE p.email = '<EMAIL>';

-- 6. Check products and their store relationships
SELECT 'PRODUCTS:' as section;
SELECT p.id, p.name, p.store_id, s.name as store_name
FROM products p
LEFT JOIN stores s ON p.store_id = s.id
ORDER BY p.created_at DESC
LIMIT 10;

-- 7. Check if there are any orders with items from stores <NAME_EMAIL>
SELECT '<NAME_EMAIL> STORES:' as section;
SELECT DISTINCT o.id, o.status, o.total, o.created_at
FROM orders o
JOIN order_items oi ON o.id = oi.order_id
JOIN stores s ON oi.store_id = s.id
JOIN profiles p ON s.owner_id = p.id
WHERE p.email = '<EMAIL>'
ORDER BY o.created_at DESC;
