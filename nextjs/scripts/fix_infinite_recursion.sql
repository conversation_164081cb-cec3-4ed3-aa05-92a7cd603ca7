-- Fix infinite recursion in profiles policies
-- This is causing the 42P17 error

BEGIN;

-- 1. Drop ALL profiles policies that might cause recursion
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "<PERSON><PERSON> can view all profiles" ON profiles;
DROP POLICY IF EXISTS "<PERSON><PERSON> can update all profiles" ON profiles;
DROP POLICY IF EXISTS "<PERSON><PERSON> can view all profiles fixed" ON profiles;
DROP POLICY IF EXISTS "Admin email can view all profiles" ON profiles;
DROP POLICY IF EXISTS "Admin email can update all profiles" ON profiles;

-- 2. Temporarily disable <PERSON><PERSON> on profiles to break the recursion
ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;

-- 3. Re-enable RLS
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- 4. Create simple, non-recursive policies
-- Users can view their own profile (no recursion)
CREATE POLICY "users_own_profile_select" ON profiles
  FOR SELECT
  USING (auth.uid() = id);

-- Users can update their own profile (no recursion)  
CREATE POLICY "users_own_profile_update" ON profiles
  FOR UPDATE
  USING (auth.uid() = id);

-- Users can insert their own profile (for registration)
CREATE POLICY "users_own_profile_insert" ON profiles
  FOR INSERT
  WITH CHECK (auth.uid() = id);

-- Admin access using email check (avoid recursion by not querying profiles table)
-- This uses the auth.email() function instead of querying profiles
CREATE POLICY "admin_email_access" ON profiles
  FOR ALL
  USING (auth.email() = '<EMAIL>');

-- 5. Also fix any other policies that might reference profiles table
-- Drop and recreate store policies without profile recursion
DROP POLICY IF EXISTS "store_owners_manage_stores" ON stores;
DROP POLICY IF EXISTS "Store owners can view their stores" ON stores;
DROP POLICY IF EXISTS "Store owners can update their stores" ON stores;
DROP POLICY IF EXISTS "Admins can insert stores" ON stores;

-- Simple store policies
CREATE POLICY "store_owners_manage_stores_simple" ON stores
  FOR ALL TO authenticated
  USING (
    owner_id = auth.uid() OR 
    auth.email() = '<EMAIL>'
  );

-- 6. Fix product policies to avoid profile recursion
DROP POLICY IF EXISTS "store_owners_manage_products" ON products;
DROP POLICY IF EXISTS "Store owners can manage their products" ON products;

CREATE POLICY "store_owners_manage_products_simple" ON products
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM stores 
      WHERE id = products.store_id AND owner_id = auth.uid()
    ) OR
    auth.email() = '<EMAIL>'
  );

-- 7. Fix category policies
DROP POLICY IF EXISTS "admins_manage_categories" ON categories;
DROP POLICY IF EXISTS "Admins can manage categories" ON categories;

CREATE POLICY "admins_manage_categories_simple" ON categories
  FOR ALL TO authenticated
  USING (auth.email() = '<EMAIL>');

-- 8. Fix order policies
DROP POLICY IF EXISTS "users_view_own_orders" ON orders;
DROP POLICY IF EXISTS "admins_update_orders" ON orders;
DROP POLICY IF EXISTS "Users can view their orders" ON orders;
DROP POLICY IF EXISTS "Admins can update orders" ON orders;

CREATE POLICY "users_view_own_orders_simple" ON orders
  FOR SELECT TO authenticated
  USING (
    user_id = auth.uid() OR 
    auth.email() = '<EMAIL>'
  );

CREATE POLICY "users_create_orders_simple" ON orders
  FOR INSERT TO authenticated
  WITH CHECK (user_id = auth.uid());

CREATE POLICY "admins_update_orders_simple" ON orders
  FOR UPDATE TO authenticated
  USING (auth.email() = '<EMAIL>');

-- 9. Fix notification policies to avoid recursion
DROP POLICY IF EXISTS "users_view_own_notifications" ON notifications;
DROP POLICY IF EXISTS "users_update_own_notifications" ON notifications;
DROP POLICY IF EXISTS "system_insert_notifications" ON notifications;

CREATE POLICY "users_view_own_notifications_simple" ON notifications
  FOR SELECT TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "users_update_own_notifications_simple" ON notifications
  FOR UPDATE TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "system_insert_notifications_simple" ON notifications
  FOR INSERT TO authenticated
  WITH CHECK (true);

-- 10. Handle order_store_items if it exists
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'order_store_items') THEN
    DROP POLICY IF EXISTS "store_owners_view_order_store_items" ON order_store_items;
    DROP POLICY IF EXISTS "store_owners_update_order_store_items" ON order_store_items;
    DROP POLICY IF EXISTS "Store owners can view their order items" ON order_store_items;
    DROP POLICY IF EXISTS "Store owners can update their order items" ON order_store_items;
    
    CREATE POLICY "store_owners_view_order_store_items_simple" ON order_store_items
      FOR SELECT TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM stores 
          WHERE id = order_store_items.store_id AND owner_id = auth.uid()
        ) OR
        auth.email() = '<EMAIL>'
      );
      
    CREATE POLICY "store_owners_update_order_store_items_simple" ON order_store_items
      FOR UPDATE TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM stores 
          WHERE id = order_store_items.store_id AND owner_id = auth.uid()
        ) OR
        auth.email() = '<EMAIL>'
      );
  END IF;
END
$$;

COMMIT;

-- Verify no recursive policies exist
SELECT schemaname, tablename, policyname, cmd, qual 
FROM pg_policies 
WHERE schemaname = 'public' 
AND qual LIKE '%profiles%'
ORDER BY tablename, policyname;




