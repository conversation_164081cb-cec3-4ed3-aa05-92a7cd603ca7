-- EMERGENCY FIX: Remove infinite recursion in profiles policies
-- This script fixes the circular reference issue

-- 1. DISABLE RLS TEMPORARILY TO BREAK THE RECURSION
ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;

-- 2. DROP ALL EXISTING POLICIES TO START FRESH
DROP POLICY IF EXISTS "Ad<PERSON> can view all profiles" ON profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can update all profiles" ON profiles;
DROP POLICY IF EXISTS "Admins can view all profiles fixed" ON profiles;
DROP POLICY IF EXISTS "Admins can update all profiles fixed" ON profiles;
DROP POLICY IF EXISTS "Users can view their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON profiles;

-- 3. CREATE SIMPLE NON-RECURSIVE POLICIES
-- Allow users to view their own profile
CREATE POLICY "Users can view own profile"
  ON profiles FOR SELECT
  TO authenticated
  USING (auth.uid() = id);

-- Allow users to update their own profile
CREATE POLICY "Users can update own profile"
  ON profiles FOR UPDATE
  TO authenticated
  USING (auth.uid() = id);

-- Allow specific admin user (by UUI<PERSON>) to view all profiles
-- We'll use the actual UUID instead of checking role to avoid recursion
CREATE POLICY "Admin can view all profiles"
  ON profiles FOR SELECT
  TO authenticated
  USING (
    auth.uid() = id OR
    auth.uid() = '46bd8f46-8417-4d20-807a-c219101a8868'::uuid
  );

-- Allow specific admin user to update all profiles
CREATE POLICY "Admin can update all profiles"
  ON profiles FOR UPDATE
  TO authenticated
  USING (
    auth.uid() = id OR
    auth.uid() = '46bd8f46-8417-4d20-807a-c219101a8868'::uuid
  );

-- 4. RE-ENABLE RLS
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- 5. SET ADMIN ROLE FOR THE SPECIFIC USER
UPDATE profiles
SET role = 'admin'
WHERE id = '46bd8f46-8417-4d20-807a-c219101a8868'::uuid;

-- 6. VERIFICATION
SELECT 'Policies fixed successfully!' as status;
SELECT policyname FROM pg_policies
WHERE tablename = 'profiles'
ORDER BY policyname;
