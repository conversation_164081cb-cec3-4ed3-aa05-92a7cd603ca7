#!/bin/bash

# <PERSON>ript to fix TypeScript code issues related to UUID handling

echo "Fixing TypeScript code issues related to UUID handling..."

# 1. Update ecommerce-client.ts
echo "Updating ecommerce-client.ts..."
sed -i '' 's/productId: number/productId: string | number/g' src/lib/services/ecommerce-client.ts
sed -i '' 's/cartItemId: number/cartItemId: string | number/g' src/lib/services/ecommerce-client.ts
sed -i '' 's/wishlistItemId: number/wishlistItemId: string | number/g' src/lib/services/ecommerce-client.ts

# 2. Update ecommerce.ts
echo "Updating ecommerce.ts..."
sed -i '' 's/productId: number/productId: string | number/g' src/lib/services/ecommerce.ts
sed -i '' 's/cartItemId: number/cartItemId: string | number/g' src/lib/services/ecommerce.ts
sed -i '' 's/wishlistItemId: number/wishlistItemId: string | number/g' src/lib/services/ecommerce.ts

# 3. Update ProductCard.tsx
echo "Updating ProductCard.tsx..."
sed -i '' 's/await EcommerceClientService.addToCart(Number(id), 1);/await EcommerceClientService.addToCart(id, 1);/g' src/features/products/components/ProductCard.tsx

# 4. Update ProductDetails.tsx
echo "Updating ProductDetails.tsx..."
sed -i '' 's/await EcommerceClientService.addToCart(Number(id), quantity);/await EcommerceClientService.addToCart(id, quantity);/g' src/features/products/components/ProductDetails.tsx

# 5. Update ecommerce.ts types
echo "Updating ecommerce.ts types..."
sed -i '' 's/product_id: number;/product_id: string | number;/g' src/lib/types/ecommerce.ts
sed -i '' 's/id: number;/id: string | number;/g' src/lib/types/ecommerce.ts

echo "Code fixes completed!"
echo "Please run the database fix script to ensure database schema is updated as well."
