-- Fix RLS policies to restore site functionality
-- This script fixes the restrictive policies that are blocking data access

-- 1. Fix stores policies - allow public access to all stores
DROP POLICY IF EXISTS "Anyone can view active stores" ON stores;
DROP POLICY IF EXISTS "Store owners can view their stores" ON stores;

-- Create a more permissive policy for stores
CREATE POLICY "Public can view all stores"
  ON stores FOR SELECT
  USING (true);

CREATE POLICY "Store owners can view their stores"
  ON stores FOR SELECT
  TO authenticated
  USING (
    owner_id = auth.uid() OR
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- 2. Fix products policies - ensure public access
DROP POLICY IF EXISTS "Anyone can view products" ON products;
DROP POLICY IF EXISTS "Store owners can manage their products" ON products;

-- Create permissive policies for products
CREATE POLICY "Public can view all products"
  ON products FOR SELECT
  USING (true);

CREATE POLICY "Store owners can manage their products"
  ON products FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM stores
      WHERE id = store_id AND owner_id = auth.uid()
    ) OR
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- 3. Add categories policies if missing
DROP POLICY IF EXISTS "Public can view categories" ON categories;
CREATE POLICY "Public can view categories"
  ON categories FOR SELECT
  USING (true);

-- Allow admins to manage categories
CREATE POLICY "Admins can manage categories"
  ON categories FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- 4. Add product_images policies if the table exists
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'product_images') THEN
    -- Drop existing policies
    DROP POLICY IF EXISTS "Public can view product images" ON product_images;
    
    -- Create new policy
    CREATE POLICY "Public can view product images"
      ON product_images FOR SELECT
      USING (true);
      
    -- Allow admins and store owners to manage product images
    CREATE POLICY "Store owners can manage product images"
      ON product_images FOR ALL
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM products p
          JOIN stores s ON p.store_id = s.id
          WHERE p.id = product_images.product_id 
          AND s.owner_id = auth.uid()
        ) OR
        EXISTS (
          SELECT 1 FROM profiles
          WHERE id = auth.uid() AND role = 'admin'
        )
      );
      
    RAISE NOTICE 'Added policies for product_images table';
  ELSE
    RAISE NOTICE 'product_images table does not exist, skipping';
  END IF;
END
$$;

-- 5. Add order_items policies if the table exists
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'order_items') THEN
    -- Drop existing policies
    DROP POLICY IF EXISTS "Users can view their order items" ON order_items;
    DROP POLICY IF EXISTS "Store owners can view their order items" ON order_items;
    
    -- Create new policies
    CREATE POLICY "Users can view their order items"
      ON order_items FOR SELECT
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM orders
          WHERE id = order_items.order_id AND user_id = auth.uid()
        ) OR
        EXISTS (
          SELECT 1 FROM stores
          WHERE id = order_items.store_id AND owner_id = auth.uid()
        ) OR
        EXISTS (
          SELECT 1 FROM profiles
          WHERE id = auth.uid() AND role = 'admin'
        )
      );
      
    CREATE POLICY "Users can insert their order items"
      ON order_items FOR INSERT
      TO authenticated
      WITH CHECK (
        EXISTS (
          SELECT 1 FROM orders
          WHERE id = order_items.order_id AND user_id = auth.uid()
        )
      );
      
    RAISE NOTICE 'Added policies for order_items table';
  ELSE
    RAISE NOTICE 'order_items table does not exist, skipping';
  END IF;
END
$$;

-- 6. Add wishlist policies if the table exists
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'wishlist') THEN
    -- Drop existing policies
    DROP POLICY IF EXISTS "Users can manage their wishlist" ON wishlist;
    
    -- Create new policies
    CREATE POLICY "Users can manage their wishlist"
      ON wishlist FOR ALL
      TO authenticated
      USING (user_id = auth.uid());
      
    RAISE NOTICE 'Added policies for wishlist table';
  ELSE
    RAISE NOTICE 'wishlist table does not exist, skipping';
  END IF;
END
$$;

-- 7. Add reviews policies if the table exists
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'reviews') THEN
    -- Drop existing policies
    DROP POLICY IF EXISTS "Public can view reviews" ON reviews;
    DROP POLICY IF EXISTS "Users can manage their reviews" ON reviews;
    
    -- Create new policies
    CREATE POLICY "Public can view reviews"
      ON reviews FOR SELECT
      USING (true);
      
    CREATE POLICY "Users can manage their reviews"
      ON reviews FOR ALL
      TO authenticated
      USING (user_id = auth.uid());
      
    RAISE NOTICE 'Added policies for reviews table';
  ELSE
    RAISE NOTICE 'reviews table does not exist, skipping';
  END IF;
END
$$;

-- 8. Add payments and payouts policies if they exist
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'payments') THEN
    -- Drop existing policies
    DROP POLICY IF EXISTS "Users can view their payments" ON payments;
    DROP POLICY IF EXISTS "Admins can manage payments" ON payments;
    
    -- Create new policies
    CREATE POLICY "Users can view their payments"
      ON payments FOR SELECT
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM orders
          WHERE id = payments.order_id AND user_id = auth.uid()
        ) OR
        EXISTS (
          SELECT 1 FROM profiles
          WHERE id = auth.uid() AND role = 'admin'
        )
      );
      
    CREATE POLICY "Admins can manage payments"
      ON payments FOR ALL
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM profiles
          WHERE id = auth.uid() AND role = 'admin'
        )
      );
      
    RAISE NOTICE 'Added policies for payments table';
  ELSE
    RAISE NOTICE 'payments table does not exist, skipping';
  END IF;
END
$$;

DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'payouts') THEN
    -- Drop existing policies
    DROP POLICY IF EXISTS "Store owners can view their payouts" ON payouts;
    DROP POLICY IF EXISTS "Admins can manage payouts" ON payouts;
    
    -- Create new policies
    CREATE POLICY "Store owners can view their payouts"
      ON payouts FOR SELECT
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM stores
          WHERE id = payouts.store_id AND owner_id = auth.uid()
        ) OR
        EXISTS (
          SELECT 1 FROM profiles
          WHERE id = auth.uid() AND role = 'admin'
        )
      );
      
    CREATE POLICY "Admins can manage payouts"
      ON payouts FOR ALL
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM profiles
          WHERE id = auth.uid() AND role = 'admin'
        )
      );
      
    RAISE NOTICE 'Added policies for payouts table';
  ELSE
    RAISE NOTICE 'payouts table does not exist, skipping';
  END IF;
END
$$;

COMMIT;
