-- Fix for the cart table issues
-- This script addresses the following issues:
-- 1. Missing options column
-- 2. Type mismatch between product_id and products.id
-- 3. NULL product_id values

-- 1. Check if options column exists in cart table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'cart' AND column_name = 'options'
    ) THEN
        -- Add options column to cart table if it doesn't exist
        ALTER TABLE cart ADD COLUMN options JSONB;
        RAISE NOTICE 'Added options column to cart table';
    ELSE
        RAISE NOTICE 'options column already exists in cart table';
    END IF;
END $$;

-- 2. Check product_id column type in cart table
DO $$
DECLARE
    cart_type TEXT;
    product_type TEXT;
BEGIN
    -- Get the data type of product_id in cart table
    SELECT data_type INTO cart_type
    FROM information_schema.columns
    WHERE table_name = 'cart' AND column_name = 'product_id';
    
    -- Get the data type of id in products table
    SELECT data_type INTO product_type
    FROM information_schema.columns
    WHERE table_name = 'products' AND column_name = 'id';
    
    RAISE NOTICE 'Cart product_id type: %, Products id type: %', cart_type, product_type;
    
    -- If there's a type mismatch, we need to fix it
    IF cart_type != product_type THEN
        RAISE NOTICE 'Type mismatch detected between cart.product_id and products.id';
        
        -- First, remove any foreign key constraints
        EXECUTE (
            SELECT 'ALTER TABLE cart DROP CONSTRAINT ' || conname
            FROM pg_constraint
            WHERE conrelid = 'cart'::regclass
            AND contype = 'f'
            AND conname LIKE '%product%'
            LIMIT 1
        );
        
        -- Remove any unique constraints that include product_id
        EXECUTE (
            SELECT 'ALTER TABLE cart DROP CONSTRAINT ' || conname
            FROM pg_constraint
            WHERE conrelid = 'cart'::regclass
            AND contype = 'u'
            AND conname LIKE '%product%'
            LIMIT 1
        );
        
        -- Drop any indexes that include product_id
        EXECUTE (
            SELECT 'DROP INDEX IF EXISTS ' || indexname
            FROM pg_indexes
            WHERE tablename = 'cart'
            AND indexname LIKE '%product%'
            LIMIT 1
        );
        
        -- Temporarily allow NULL values in product_id
        ALTER TABLE cart ALTER COLUMN product_id DROP NOT NULL;
        
        -- Create a temporary column with the correct type
        EXECUTE 'ALTER TABLE cart ADD COLUMN product_id_new ' || product_type;
        
        -- Update the new column with converted values
        UPDATE cart SET product_id_new = product_id::text::" || product_type || " WHERE product_id IS NOT NULL;
        
        -- Drop the old column and rename the new one
        ALTER TABLE cart DROP COLUMN product_id;
        ALTER TABLE cart RENAME COLUMN product_id_new TO product_id;
        
        -- Add NOT NULL constraint back
        ALTER TABLE cart ALTER COLUMN product_id SET NOT NULL;
        
        -- Recreate the unique constraint
        ALTER TABLE cart ADD CONSTRAINT cart_user_product_key UNIQUE (user_id, product_id);
        
        -- Recreate the foreign key constraint
        ALTER TABLE cart ADD CONSTRAINT cart_product_id_fkey FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE;
        
        RAISE NOTICE 'Fixed type mismatch between cart.product_id and products.id';
    ELSE
        RAISE NOTICE 'No type mismatch detected between cart.product_id and products.id';
    END IF;
END $$;

-- 3. Check for NULL product_id values
DO $$
DECLARE
    null_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO null_count
    FROM cart
    WHERE product_id IS NULL;
    
    IF null_count > 0 THEN
        RAISE NOTICE 'Found % cart items with NULL product_id', null_count;
        
        -- Delete cart items with NULL product_id
        DELETE FROM cart WHERE product_id IS NULL;
        RAISE NOTICE 'Deleted % cart items with NULL product_id', null_count;
    ELSE
        RAISE NOTICE 'No cart items with NULL product_id found';
    END IF;
END $$;

-- Display current cart schema for verification
SELECT column_name, data_type, column_default, is_nullable
FROM information_schema.columns
WHERE table_name = 'cart'
ORDER BY ordinal_position;
