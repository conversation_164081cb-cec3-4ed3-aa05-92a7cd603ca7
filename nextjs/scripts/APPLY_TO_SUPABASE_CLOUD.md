# Apply Database Schema to Supabase Cloud

The "Order Not Found" error occurs because the database tables don't exist in your Supabase cloud database. You need to apply the database schema.

## Quick Fix (Recommended)

1. **Go to your Supabase Dashboard**
   - Visit https://supabase.com/dashboard
   - Select your project

2. **Open SQL Editor**
   - Click on "SQL Editor" in the left sidebar
   - Click "New query"

3. **Copy and paste the entire content of this file:**
   ```
   supabase/migrations/finder_schema.sql
   ```

4. **Run the SQL**
   - Click "Run" to execute the schema
   - This will create all necessary tables (orders, order_items, payments, etc.)

## Alternative: Use the Script

If you prefer to use a script:

1. **Make sure you have the required environment variables in `.env.local`:**
   ```
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
   ```

2. **Run the schema application script:**
   ```bash
   cd nextjs
   node scripts/apply-schema-to-cloud.js
   ```

## Verify the Fix

After applying the schema:

1. **Check your Supabase Dashboard**
   - Go to "Table Editor"
   - You should see tables like: orders, order_items, payments, profiles, stores, products, etc.

2. **Test the admin panel**
   - Visit http://localhost:3000/admin/orders
   - The "Order Not Found" error should be resolved

3. **Check the API endpoints**
   - Visit http://localhost:3000/api/admin/debug
   - Should show that tables exist

## What This Creates

The schema creates these main tables:
- `profiles` - User profiles
- `categories` - Product categories  
- `stores` - Store information
- `products` - Product catalog
- `orders` - Customer orders
- `order_items` - Items in each order
- `payments` - Payment records
- `payouts` - Store payouts
- `notifications` - System notifications
- `cart` - Shopping cart
- `wishlist` - User wishlists
- `reviews` - Product reviews

## Troubleshooting

If you get permission errors:
1. Make sure you're using the SERVICE_ROLE_KEY (not the anon key)
2. The service role key should start with `eyJ...`
3. Check that your Supabase URL is correct

If tables already exist:
- The schema uses `CREATE TABLE IF NOT EXISTS` so it's safe to run multiple times
- Existing data won't be affected

## Next Steps

After the schema is applied:
1. You can create test orders through the application
2. The admin panel will work properly
3. All order management features will be functional
