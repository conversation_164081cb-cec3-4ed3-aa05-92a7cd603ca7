-- Check for potential UUID issues

-- Check products table ID type
SELECT column_name, data_type
FROM information_schema.columns
WHERE table_name = 'products' AND column_name = 'id';

-- Check cart table product_id type
SELECT column_name, data_type
FROM information_schema.columns
WHERE table_name = 'cart' AND column_name = 'product_id';

-- Check if there are any cart entries with invalid product_id values
SELECT id, user_id, product_id
FROM cart
WHERE product_id::text = 'NaN' OR product_id IS NULL;

-- Check for type mismatches between cart.product_id and products.id
DO $$
DECLARE
    cart_type TEXT;
    products_type TEXT;
BEGIN
    -- Get the data type of cart.product_id
    SELECT data_type INTO cart_type
    FROM information_schema.columns
    WHERE table_name = 'cart' AND column_name = 'product_id';
    
    -- Get the data type of products.id
    SELECT data_type INTO products_type
    FROM information_schema.columns
    WHERE table_name = 'products' AND column_name = 'id';
    
    -- Output the types
    RAISE NOTICE 'cart.product_id type: %, products.id type: %', cart_type, products_type;
    
    -- Check if there's a mismatch
    IF cart_type <> products_type THEN
        RAISE NOTICE 'Type mismatch detected between cart.product_id (%) and products.id (%)', cart_type, products_type;
    ELSE
        RAISE NOTICE 'Types match between cart.product_id and products.id';
    END IF;
END $$;
