-- Fix for the NULL product_id issue in cart table

-- 1. Check the current schema of the cart table
SELECT column_name, data_type, is_nullable
FROM information_schema.columns
WHERE table_name = 'cart'
ORDER BY ordinal_position;

-- 2. Check for any NULL product_id values in the cart table
SELECT id, user_id, product_id
FROM cart
WHERE product_id IS NULL;

-- 3. Fix the issue by either:
-- a) Removing the NOT NULL constraint temporarily, or
-- b) Deleting cart entries with NULL product_id

-- Option A: Temporarily remove the NOT NULL constraint
DO $$
BEGIN
    -- First, check if the column is NOT NULL
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'cart' 
        AND column_name = 'product_id'
        AND is_nullable = 'NO'
    ) THEN
        -- Drop foreign key constraint
        ALTER TABLE cart DROP CONSTRAINT IF EXISTS cart_product_id_fkey;
        
        -- Drop unique constraint that includes product_id
        ALTER TABLE cart DROP CONSTRAINT IF EXISTS cart_user_product_key;
        DROP INDEX IF EXISTS cart_user_product_key;
        
        -- Alter the column to allow NULL values temporarily
        ALTER TABLE cart ALTER COLUMN product_id DROP NOT NULL;
        
        RAISE NOTICE 'Temporarily removed NOT NULL constraint from product_id column';
        
        -- Recreate the unique constraint with a WHERE clause to exclude NULL values
        ALTER TABLE cart ADD CONSTRAINT cart_user_product_key 
        UNIQUE (user_id, product_id) 
        WHERE product_id IS NOT NULL;
        
        -- Recreate the foreign key constraint
        ALTER TABLE cart ADD CONSTRAINT cart_product_id_fkey 
        FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE;
        
        RAISE NOTICE 'Recreated constraints to handle NULL values';
    ELSE
        RAISE NOTICE 'product_id column already allows NULL values';
    END IF;
END $$;

-- Option B: Delete cart entries with NULL product_id
-- DELETE FROM cart WHERE product_id IS NULL;

-- 4. Check the updated schema
SELECT column_name, data_type, is_nullable
FROM information_schema.columns
WHERE table_name = 'cart'
ORDER BY ordinal_position;

-- 5. Check if there are still any NULL product_id values
SELECT COUNT(*) AS null_product_id_count
FROM cart
WHERE product_id IS NULL;

-- Note: After fixing the immediate issue, you should update your TypeScript code
-- to ensure it properly handles UUID product IDs instead of numbers.
-- See the fix_code.sh script for automated fixes to your TypeScript code.
