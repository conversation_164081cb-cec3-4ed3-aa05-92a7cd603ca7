-- Fix the ambiguous product_id reference in the get_order_details function
CREATE OR R<PERSON>LACE FUNCTION get_order_details(order_id_param UUID)
RETURNS TABLE (
  -- Order fields
  id UUID,
  user_id UUID,
  status TEXT,
  total DECIMAL,
  currency TEXT,
  shipping_name TEXT,
  shipping_email TEXT,
  shipping_phone TEXT,
  shipping_address TEXT,
  shipping_city TEXT,
  shipping_state TEXT,
  shipping_country TEXT,
  shipping_postal_code TEXT,
  notes TEXT,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ,
  user_email TEXT,
  
  -- Order item fields
  item_id UUID,
  product_id UUID,
  store_id UUID,
  quantity INTEGER,
  price DECIMAL,
  item_total DECIMAL,
  
  -- Product fields
  product_name TEXT,
  product_image TEXT,
  
  -- Store fields
  store_name TEXT
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    o.id,
    o.user_id,
    o.status,
    o.total,
    o.currency,
    o.shipping_name,
    o.shipping_email,
    o.shipping_phone,
    o.shipping_address,
    o.shipping_city,
    o.shipping_state,
    o.shipping_country,
    o.shipping_postal_code,
    o.notes,
    o.created_at,
    o.updated_at,
    p.email AS user_email,
    
    oi.id AS item_id,
    oi.product_id,
    oi.store_id,
    oi.quantity,
    oi.price,
    oi.total AS item_total,
    
    prod.name AS product_name,
    (SELECT url FROM product_images WHERE product_images.product_id = prod.id LIMIT 1) AS product_image,
    
    s.name AS store_name
  FROM
    orders o
  LEFT JOIN
    profiles p ON o.user_id = p.id
  LEFT JOIN
    order_items oi ON o.id = oi.order_id
  LEFT JOIN
    products prod ON oi.product_id = prod.id
  LEFT JOIN
    stores s ON oi.store_id = s.id
  WHERE
    o.id = order_id_param;
END;
$$;
