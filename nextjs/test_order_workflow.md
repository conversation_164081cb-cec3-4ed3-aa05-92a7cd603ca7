# Order Workflow Testing Guide

## Prerequisites

1. **Database Setup**: Run the SQL script `order_workflow_schema.sql` in your Supabase SQL editor
2. **User Roles**: Ensure you have users with proper roles:
   - Admin user with `role = 'admin'` in profiles table
   - Store owner user with `role = 'store_owner'` in profiles table
   - Regular user with `role = 'user'` in profiles table
3. **Store Setup**: Ensure the store owner has a store with `owner_id` set to their user ID

## Testing Steps

### Step 1: Create a Test Order
1. Log in as a regular user
2. Add products to cart from different stores
3. Complete checkout to create an order
4. Order should be created with status `pending`

### Step 2: Admin Reviews Order
1. Log in as admin user
2. Go to `/admin/orders`
3. Find the new order (should show status "Pending")
4. Click on the order to view details
5. Click **"Start Review"** button
6. Verify:
   - Order status changes to "Under Review"
   - User receives notification about order being reviewed
   - Store owners receive notifications about new order to review

### Step 3: Store Owner Confirms Order
1. Log in as store owner
2. Go to `/admin/store-orders` (should see "Store Orders" in navigation)
3. Find the order (should show status "Awaiting Store Confirmation")
4. Click on the order to view details
5. Click **"Confirm Order"** button
6. Verify:
   - Order status changes to "Accepted by Store"
   - Admin receives notification about store confirmation

### Step 4: Admin Confirms Payment
1. Log back in as admin
2. Go to the order details page
3. Order should now show "Accepted by Store" status
4. Click **"Confirm Payment"** button
5. Verify:
   - Order status changes to "Processing"
   - Store owner receives payment confirmation notification
   - User receives order processing notification

### Step 5: Continue Normal Workflow
1. Admin can continue with normal order workflow:
   - Mark as "Shipped"
   - Mark as "Delivered"

## Expected Notifications

### User Notifications:
- "Your order is being reviewed by our team" (Step 2)
- "Your order has been confirmed by the store and is being processed" (Step 4)

### Store Owner Notifications:
- "A new order #XXXXXXXX requires your confirmation. Please review and confirm availability." (Step 2)
- "Payment of GMD XXX for order #XXXXXXXX has been confirmed. Order is being processed." (Step 4)

### Admin Notifications:
- "Store Name has confirmed order #XXXXXXXX and it's ready for processing." (Step 3)

## Troubleshooting

### Common Issues:

1. **TypeScript Errors**: The current implementation has TypeScript errors due to Supabase schema mismatches. These are expected and won't prevent functionality.

2. **Missing Tables**: If you get errors about missing tables, ensure you've run the SQL schema script.

3. **Permission Errors**: Ensure Row Level Security policies are properly set up for the new tables.

4. **Navigation Not Showing**: Store owners should see "Store Orders" instead of "Orders & Payments" in the admin navigation.

### Database Verification Queries:

```sql
-- Check order status
SELECT id, status, created_at FROM orders ORDER BY created_at DESC LIMIT 5;

-- Check order store items
SELECT * FROM order_store_items ORDER BY created_at DESC LIMIT 5;

-- Check notifications
SELECT title, content, type, created_at FROM notifications ORDER BY created_at DESC LIMIT 10;

-- Check user roles
SELECT email, role FROM profiles WHERE role IN ('admin', 'store_owner');

-- Check store ownership
SELECT s.name, s.owner_id, p.email 
FROM stores s 
JOIN profiles p ON s.owner_id = p.id;
```

## Manual Testing Checklist

- [ ] Order creation works
- [ ] Admin can start review
- [ ] User gets review notification
- [ ] Store owner gets review notification
- [ ] Store owner can see orders in `/admin/store-orders`
- [ ] Store owner can confirm orders
- [ ] Admin gets store confirmation notification
- [ ] Admin can confirm payment
- [ ] Store owner gets payment notification
- [ ] User gets processing notification
- [ ] Order progresses through all statuses correctly

## Next Steps

After testing, you may want to:

1. **Fix TypeScript Errors**: Update Supabase types to match your actual database schema
2. **Add Order Rejection**: Implement store owner order rejection functionality
3. **Add Email Notifications**: Integrate with email service for notifications
4. **Add Real-time Updates**: Use Supabase real-time subscriptions for live updates
5. **Add Order Tracking**: Implement order tracking for customers
6. **Add Inventory Management**: Check inventory when store owners confirm orders
