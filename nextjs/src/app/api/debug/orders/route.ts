import { NextRequest, NextResponse } from 'next/server';
import { createSPASassClient } from '@/lib/supabase/client';

export async function GET(request: NextRequest) {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    // Get current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    // Get user profile
    const { data: profile } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single();

    // Get user's stores
    const { data: stores } = await supabase
      .from('stores')
      .select('*')
      .eq('owner_id', user.id);

    // Get all orders
    const { data: allOrders } = await supabase
      .from('orders')
      .select('*')
      .limit(5);

    // Get all order items
    const { data: allOrderItems } = await supabase
      .from('order_items')
      .select('*')
      .limit(10);

    // Get all stores
    const { data: allStores } = await supabase
      .from('stores')
      .select('*')
      .limit(5);

    return NextResponse.json({
      user: {
        id: user.id,
        email: user.email
      },
      profile,
      userStores: stores,
      allOrders,
      allOrderItems,
      allStores
    });

  } catch (error) {
    console.error('Debug error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
