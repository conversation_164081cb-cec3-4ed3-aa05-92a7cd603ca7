import { NextRequest, NextResponse } from 'next/server';
import { createSSRClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    const supabase = await createSSRClient();
    
    // Check what tables exist and their structure
    const results: any = {};
    
    // Check orders table
    try {
      const { data: orders, error: ordersError } = await supabase
        .from('orders')
        .select('*')
        .limit(5);
      
      results.orders = {
        exists: !ordersError,
        error: ordersError?.message,
        count: orders?.length || 0,
        sample: orders?.slice(0, 2) || []
      };
    } catch (e) {
      results.orders = { exists: false, error: (e as Error).message };
    }
    
    // Check order_items table
    try {
      const { data: orderItems, error: orderItemsError } = await supabase
        .from('order_items')
        .select('*')
        .limit(5);
      
      results.order_items = {
        exists: !orderItemsError,
        error: orderItemsError?.message,
        count: orderItems?.length || 0,
        sample: orderItems?.slice(0, 2) || []
      };
    } catch (e) {
      results.order_items = { exists: false, error: (e as Error).message };
    }
    
    // Check payments table
    try {
      const { data: payments, error: paymentsError } = await supabase
        .from('payments')
        .select('*')
        .limit(5);
      
      results.payments = {
        exists: !paymentsError,
        error: paymentsError?.message,
        count: payments?.length || 0,
        sample: payments?.slice(0, 2) || []
      };
    } catch (e) {
      results.payments = { exists: false, error: (e as Error).message };
    }
    
    // Check profiles table
    try {
      const { data: profiles, error: profilesError } = await supabase
        .from('profiles')
        .select('*')
        .limit(5);
      
      results.profiles = {
        exists: !profilesError,
        error: profilesError?.message,
        count: profiles?.length || 0,
        sample: profiles?.slice(0, 2) || []
      };
    } catch (e) {
      results.profiles = { exists: false, error: (e as Error).message };
    }

    return NextResponse.json(results);
  } catch (error) {
    console.error('Debug API Error:', error);
    return NextResponse.json({ error: 'Internal server error', details: (error as Error).message }, { status: 500 });
  }
}
