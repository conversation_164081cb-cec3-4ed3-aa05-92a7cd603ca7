"use client";
import React from 'react';
import { useParams } from 'next/navigation';
import { useStore } from '@/features/stores/queries';
import { StoreGrid } from '@/features/stores/components';
import EcommerceLayout from '@/components/ecommerce/EcommerceLayout';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle } from 'lucide-react';
import Image from 'next/image';
import { Star } from 'lucide-react';

export default function StoreDetailPage() {
  const { slug } = useParams();
  const { data, isLoading, error } = useStore(slug as string);

  if (isLoading) {
    return (
      <EcommerceLayout>
        <div className="flex items-center justify-center h-96">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
        </div>
      </EcommerceLayout>
    );
  }

  if (error || !data?.store) {
    return (
      <EcommerceLayout>
        <div className="container mx-auto px-4 py-8">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              {error instanceof Error ? error.message : 'Store not found'}
            </AlertDescription>
          </Alert>
        </div>
      </EcommerceLayout>
    );
  }

  const { store, products } = data;

  return (
    <EcommerceLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="space-y-8">
          {/* Store Header */}
          <div className="relative h-48 md:h-64 rounded-lg overflow-hidden bg-gradient-to-r from-blue-500 to-purple-600">
            {store.coverImage && (
              <Image
                src={store.coverImage}
                alt={store.name}
                fill
                className="object-cover"
              />
            )}
            <div className="absolute inset-0 bg-black bg-opacity-40 flex items-end">
              <div className="p-6 w-full">
                <div className="flex items-end gap-4">
                  <div className="relative w-24 h-24 rounded-full overflow-hidden border-4 border-white bg-white">
                    {store.logo ? (
                      <Image
                        src={store.logo}
                        alt={store.name}
                        fill
                        className="object-cover"
                      />
                    ) : (
                      <div className="flex items-center justify-center h-full w-full bg-gray-100">
                        <span className="text-2xl font-bold text-gray-500">
                          {store.name.charAt(0).toUpperCase()}
                        </span>
                      </div>
                    )}
                  </div>
                  <div className="flex-1 text-white">
                    <h1 className="text-3xl font-bold">{store.name}</h1>
                    {store.rating && (
                      <div className="flex items-center gap-1 mt-1">
                        <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                        <span>{store.rating.toFixed(1)}</span>
                        {store.reviewCount && (
                          <span className="text-sm opacity-75">
                            ({store.reviewCount} reviews)
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Store Description */}
          {store.description && (
            <div className="prose max-w-none">
              <p className="text-gray-600">{store.description}</p>
            </div>
          )}

          {/* Store Products */}
          <div>
            <h2 className="text-2xl font-bold mb-6">Products</h2>
            {products && products.length > 0 ? (
              <StoreGrid
                stores={products}
                variant="default"
                columns={4}
              />
            ) : (
              <p className="text-gray-500 text-center py-8">
                No products available at the moment
              </p>
            )}
          </div>

          {/* Store Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {store.contactEmail && (
              <div>
                <h3 className="font-medium mb-2">Contact Email</h3>
                <p className="text-gray-600">{store.contactEmail}</p>
              </div>
            )}
            {store.contactPhone && (
              <div>
                <h3 className="font-medium mb-2">Contact Phone</h3>
                <p className="text-gray-600">{store.contactPhone}</p>
              </div>
            )}
            {store.address && (
              <div className="md:col-span-2">
                <h3 className="font-medium mb-2">Address</h3>
                <p className="text-gray-600">
                  {[
                    store.address,
                    store.city,
                    store.state,
                    store.postalCode,
                    store.country
                  ]
                    .filter(Boolean)
                    .join(', ')}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </EcommerceLayout>
  );
}