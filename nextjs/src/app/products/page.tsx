'use client';

import React, { useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { useProducts } from '@/features/products/queries';
import { ProductGrid } from '@/features/products/components';
import EcommerceLayout from '@/components/ecommerce/EcommerceLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Search, Filter } from 'lucide-react';
import { Pagination } from '@/components/ui/pagination';

export default function ProductsPage() {
  const searchParams = useSearchParams();
  const [searchQuery, setSearchQuery] = useState(searchParams.get('search') || '');
  const [currentPage, setCurrentPage] = useState(1);
  const [showFilters, setShowFilters] = useState(false);
  
  // Get category and store from URL if present
  const categoryId = searchParams.get('category') || undefined;
  const storeId = searchParams.get('store') || undefined;
  const featured = searchParams.get('featured') === 'true' ? true : undefined;
  
  const { data, isLoading, error } = useProducts(
    { page: currentPage, limit: 12 },
    { 
      categoryId, 
      storeId, 
      featured,
      search: searchParams.get('search') || undefined
    }
  );
  
  const products = data?.products?.data || [];
  const totalPages = data?.products?.meta?.totalPages || 1;
  
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Update URL with search query
    const url = new URL(window.location.href);
    if (searchQuery) {
      url.searchParams.set('search', searchQuery);
    } else {
      url.searchParams.delete('search');
    }
    window.history.pushState({}, '', url.toString());
    setCurrentPage(1);
  };
  
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };
  
  return (
    <EcommerceLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">All Products</h1>
          <p className="text-gray-600">
            Browse our collection of products
          </p>
        </div>
        
        {/* Search and filters */}
        <div className="mb-8">
          <div className="flex flex-col md:flex-row gap-4 mb-4">
            <form onSubmit={handleSearch} className="flex gap-2 flex-grow">
              <div className="relative flex-grow">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-500" />
                <Input
                  type="search"
                  placeholder="Search products..."
                  className="pl-10"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <Button type="submit">Search</Button>
            </form>
            
            <Button 
              variant="outline" 
              className="md:w-auto"
              onClick={() => setShowFilters(!showFilters)}
            >
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </Button>
          </div>
          
          {/* Filter options - can be expanded later */}
          {showFilters && (
            <div className="bg-gray-50 p-4 rounded-lg mb-4">
              <h3 className="font-medium mb-2">Filter Options</h3>
              {/* Add filter options here */}
              <div className="flex flex-wrap gap-2 mt-4">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => {
                    const url = new URL(window.location.href);
                    url.searchParams.delete('category');
                    url.searchParams.delete('store');
                    url.searchParams.delete('featured');
                    url.searchParams.delete('search');
                    window.history.pushState({}, '', url.toString());
                    setSearchQuery('');
                    setCurrentPage(1);
                  }}
                >
                  Clear All Filters
                </Button>
              </div>
            </div>
          )}
        </div>
        
        {/* Products */}
        {isLoading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6 animate-pulse">
            {[...Array(12)].map((_, index) => (
              <div key={index} className="bg-gray-200 rounded-lg h-72"></div>
            ))}
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <p className="text-red-500">Error loading products</p>
          </div>
        ) : products.length > 0 ? (
          <>
            <ProductGrid products={products} />
            
            {/* Pagination */}
            {totalPages > 1 && (
              <div className="mt-8 flex justify-center">
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  onPageChange={handlePageChange}
                />
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-12">
            <p className="text-gray-500">No products found</p>
            <Button 
              variant="link" 
              className="mt-2"
              onClick={() => {
                const url = new URL(window.location.href);
                url.searchParams.delete('category');
                url.searchParams.delete('store');
                url.searchParams.delete('featured');
                url.searchParams.delete('search');
                window.history.pushState({}, '', url.toString());
                setSearchQuery('');
                setCurrentPage(1);
              }}
            >
              Clear filters and try again
            </Button>
          </div>
        )}
      </div>
    </EcommerceLayout>
  );
}
