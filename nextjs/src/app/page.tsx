import React from 'react';
import EcommerceLayout from '@/components/ecommerce/EcommerceLayout';
import Hero from '@/components/ecommerce/Hero';
import Newsletter from '@/components/ecommerce/Newsletter';
import Deals from '@/components/ecommerce/Deals';

// Import our new feature components
import { FeaturedProducts } from '@/features/products/components';
import { FeaturedCategories } from '@/features/categories/components';
import { FeaturedStores, DirectFeaturedStores } from '@/features/stores/components';
import AuthDebug from '@/components/debug/AuthDebug';

export default function Home() {
  return (
    <EcommerceLayout>
      {/* Hero Section */}
      <Hero />

      {/* Featured Categories */}
      <FeaturedCategories
        title="Shop by Category"
        variant="featured"
        limit={6}
      />

      {/* Featured Products */}
      <FeaturedProducts
        title="Featured Products"
        limit={8}
      />

      {/* Trending Products - We'll use FeaturedProducts with different filters later */}
      <FeaturedProducts
        title="Trending Now"
        subtitle="Discover what's popular with our customers"
        limit={4}
      />

      {/* Stores Section */}
      <FeaturedStores
        title="Our Stores"
        subtitle="Explore our marketplace stores with unique products and excellent service."
        variant="featured"
        limit={4}
      />

      {/* Deals Section */}
      <Deals
        title="Limited Time Offers"
        subtitle="Hurry up! These deals won't last long."
      />

      {/* Newsletter */}
      <Newsletter />

      {/* Debug component - remove in production */}
      <AuthDebug />
    </EcommerceLayout>
  );
}