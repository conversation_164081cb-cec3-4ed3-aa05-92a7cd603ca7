"use client";
import React, { useState, useEffect } from 'react';
import { AdminHeader, DataTable, StatusBadge } from '@/components/admin';
import { getProducts } from '@/features/admin/api';
import { AdminProduct, ProductListParams } from '@/features/admin/types';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Edit, Eye, Star, StarOff, Tag } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { formatCurrency } from '@/lib/utils/format';
import Image from 'next/image';

export default function SynergyStorePage() {
  const [products, setProducts] = useState<AdminProduct[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [params, setParams] = useState<ProductListParams>({
    page: 1,
    per_page: 10,
    sort_by: 'created_at',
    sort_order: 'desc',
    store_id: 'synergy', // This would be the ID of the Synergy store
  });

  useEffect(() => {
    const fetchProducts = async () => {
      setLoading(true);
      try {
        const { products, count } = await getProducts(params);
        setProducts(products);
        setTotalCount(count);
      } catch (error) {
        console.error('Error fetching products:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [params]);

  const handlePageChange = (page: number) => {
    setParams({ ...params, page });
  };

  const handleSearch = (search: string) => {
    setParams({ ...params, search, page: 1 });
  };

  const handleSortChange = (key: string, order: 'asc' | 'desc') => {
    setParams({ ...params, sort_by: key, sort_order: order, page: 1 });
  };

  const handleFeaturedFilter = (featured: boolean | 'all') => {
    if (featured === 'all') {
      const { featured, ...rest } = params;
      setParams({ ...rest, page: 1 });
    } else {
      setParams({ ...params, featured, page: 1 });
    }
  };

  const handleInStockFilter = (in_stock: boolean | 'all') => {
    if (in_stock === 'all') {
      const { in_stock, ...rest } = params;
      setParams({ ...rest, page: 1 });
    } else {
      setParams({ ...params, in_stock, page: 1 });
    }
  };

  const columns = [
    {
      key: 'name',
      header: 'Product',
      cell: (product: AdminProduct) => (
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 rounded-md bg-gray-100 flex items-center justify-center">
            <Tag className="h-5 w-5 text-gray-500" />
          </div>
          <div>
            <div className="font-medium">{product.name}</div>
            <div className="text-sm text-gray-500">{product.slug}</div>
          </div>
        </div>
      ),
      sortable: true,
    },
    {
      key: 'price',
      header: 'Price',
      cell: (product: AdminProduct) => (
        <div>
          <div className="font-medium">{formatCurrency(product.price, product.currency)}</div>
          {product.compare_at_price && (
            <div className="text-sm text-gray-500 line-through">
              {formatCurrency(product.compare_at_price, product.currency)}
            </div>
          )}
        </div>
      ),
      sortable: true,
    },
    {
      key: 'category',
      header: 'Category',
      cell: (product: AdminProduct) => (
        <div>{product.category_name || 'Uncategorized'}</div>
      ),
    },
    {
      key: 'featured',
      header: 'Featured',
      cell: (product: AdminProduct) => (
        <div>
          {product.featured ? (
            <Star className="h-5 w-5 text-amber-500 fill-amber-500" />
          ) : (
            <StarOff className="h-5 w-5 text-gray-300" />
          )}
        </div>
      ),
      sortable: true,
    },
    {
      key: 'in_stock',
      header: 'Stock',
      cell: (product: AdminProduct) => (
        <StatusBadge status={product.in_stock ? 'active' : 'disabled'} />
      ),
      sortable: true,
    },
    {
      key: 'actions',
      header: 'Actions',
      cell: (product: AdminProduct) => (
        <div className="flex items-center gap-2">
          <Link href={`/admin/products/${product.id}`}>
            <Button variant="ghost" size="sm">
              <Eye className="h-4 w-4" />
            </Button>
          </Link>
          <Link href={`/admin/products/${product.id}/edit`}>
            <Button variant="ghost" size="sm">
              <Edit className="h-4 w-4" />
            </Button>
          </Link>
        </div>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <AdminHeader
        title="Synergy Store"
        description="Manage your Synergy store products"
        actionHref="/admin/products/new?store=synergy"
        actionLabel="Add Product"
      />

      <div className="flex flex-wrap items-center gap-4 mb-4">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">Featured:</span>
          <Select
            value={params.featured !== undefined ? params.featured.toString() : 'all'}
            onValueChange={(value) => handleFeaturedFilter(value === 'all' ? 'all' : value === 'true')}
          >
            <SelectTrigger className="w-32">
              <SelectValue placeholder="All" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All</SelectItem>
              <SelectItem value="true">Featured</SelectItem>
              <SelectItem value="false">Not Featured</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">Stock:</span>
          <Select
            value={params.in_stock !== undefined ? params.in_stock.toString() : 'all'}
            onValueChange={(value) => handleInStockFilter(value === 'all' ? 'all' : value === 'true')}
          >
            <SelectTrigger className="w-32">
              <SelectValue placeholder="All" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All</SelectItem>
              <SelectItem value="true">In Stock</SelectItem>
              <SelectItem value="false">Out of Stock</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <DataTable
        columns={columns}
        data={products}
        totalCount={totalCount}
        pageSize={params.per_page || 10}
        currentPage={params.page || 1}
        onPageChange={handlePageChange}
        onSearch={handleSearch}
        searchPlaceholder="Search products..."
        onSortChange={handleSortChange}
        sortKey={params.sort_by}
        sortOrder={params.sort_order}
        isLoading={loading}
      />
    </div>
  );
}
