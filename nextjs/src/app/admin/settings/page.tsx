"use client";
import React, { useState, useEffect } from 'react';
import { AdminHeader } from '@/components/admin';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/lib/hooks/useAuth';
import { featureToggleService } from '@/lib/services/feature-toggles';
import { FeatureToggle, FeatureKey, UserRole } from '@/lib/types/roles';
import { useToast } from '@/lib/hooks/use-toast';
import { Settings, ToggleLeft, ToggleRight, Users, Shield } from 'lucide-react';

export default function AdminSettingsPage() {
  const { role, hasPermission } = useAuth();
  const { toast } = useToast();
  const [featureToggles, setFeatureToggles] = useState<FeatureToggle[]>([]);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState<string | null>(null);

  useEffect(() => {
    loadFeatureToggles();
  }, []);

  const loadFeatureToggles = async () => {
    try {
      await featureToggleService.initialize();
      const toggles = featureToggleService.getAllToggles();

      if (toggles.length === 0) {
        toast({
          title: 'Notice',
          description: 'Feature toggles table not found. Using default settings.',
          variant: 'default',
        });
      }

      setFeatureToggles(toggles);
    } catch (error) {
      console.error('Error loading feature toggles:', error);
      toast({
        title: 'Warning',
        description: 'Could not load feature toggles from database. Using defaults.',
        variant: 'default',
      });
      // Set empty array to show the notice
      setFeatureToggles([]);
    } finally {
      setLoading(false);
    }
  };

  const handleToggleUpdate = async (featureKey: FeatureKey, enabled: boolean) => {
    if (!hasPermission('canManageFeatureToggles')) {
      toast({
        title: 'Access Denied',
        description: 'You do not have permission to manage feature toggles',
        variant: 'destructive',
      });
      return;
    }

    setUpdating(featureKey);
    try {
      const success = await featureToggleService.updateFeatureToggle(featureKey, enabled);

      if (success) {
        // Update local state
        setFeatureToggles(prev =>
          prev.map(toggle =>
            toggle.feature_key === featureKey
              ? { ...toggle, enabled }
              : toggle
          )
        );

        toast({
          title: 'Success',
          description: `Feature ${enabled ? 'enabled' : 'disabled'} successfully`,
          variant: 'default',
        });
      } else {
        throw new Error('Failed to update feature toggle');
      }
    } catch (error) {
      console.error('Error updating feature toggle:', error);
      toast({
        title: 'Error',
        description: 'Failed to update feature toggle',
        variant: 'destructive',
      });
    } finally {
      setUpdating(null);
    }
  };

  const getRoleColor = (role: UserRole) => {
    switch (role) {
      case 'admin': return 'bg-red-100 text-red-800';
      case 'store_owner': return 'bg-blue-100 text-blue-800';
      case 'user': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (!hasPermission('canManageFeatureToggles')) {
    return (
      <div className="space-y-6">
        <AdminHeader
          title="Settings"
          description="System settings and configuration"
          icon={Settings}
        />
        <Card>
          <CardContent className="p-6 text-center">
            <Shield className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Access Restricted</h3>
            <p className="text-gray-600">You do not have permission to access system settings.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <AdminHeader
        title="Settings"
        description="Manage system settings and feature toggles"
        icon={Settings}
      />

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ToggleLeft className="h-5 w-5" />
            Feature Toggles
          </CardTitle>
          <CardDescription>
            Control which features are available to different user roles.
            Changes take effect immediately for all users.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
            </div>
          ) : featureToggles.length === 0 ? (
            <div className="text-center py-8">
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                <h3 className="text-lg font-medium text-yellow-800 mb-2">Feature Toggles Not Available</h3>
                <p className="text-yellow-700 mb-4">
                  The feature toggles database table is not set up yet. The system is using default role-based permissions.
                </p>
                <p className="text-sm text-yellow-600">
                  To enable feature toggle management, please run the SQL script provided in the documentation.
                </p>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {featureToggles.map((toggle) => (
                <div key={toggle.feature_key} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h4 className="font-medium">{toggle.feature_name}</h4>
                      <div className="flex gap-1">
                        {toggle.role_restrictions?.map((role) => (
                          <Badge key={role} variant="outline" className={getRoleColor(role)}>
                            {role}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    {toggle.description && (
                      <p className="text-sm text-gray-600">{toggle.description}</p>
                    )}
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="text-sm text-gray-500">
                      {toggle.enabled ? 'Enabled' : 'Disabled'}
                    </span>
                    <Switch
                      checked={toggle.enabled}
                      onCheckedChange={(enabled) =>
                        handleToggleUpdate(toggle.feature_key as FeatureKey, enabled)
                      }
                      disabled={updating === toggle.feature_key}
                    />
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Role Information
          </CardTitle>
          <CardDescription>
            Current role-based access control information
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="font-medium">Your Role:</span>
              <Badge className={getRoleColor(role)}>{role}</Badge>
            </div>
            <div className="text-sm text-gray-600">
              <p><strong>Admin:</strong> Full access to all features and settings</p>
              <p><strong>Store Owner:</strong> Access to store management features only</p>
              <p><strong>User:</strong> Basic user access, no admin features</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
