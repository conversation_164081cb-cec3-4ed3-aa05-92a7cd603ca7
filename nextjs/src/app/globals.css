@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
    /* Color scheme variables */
    --color-background: #ffffff;
    --color-foreground: #0f172a;

    /* Primary colors */
    --color-primary-50: #f0f9ff;
    --color-primary-100: #e0f2fe;
    --color-primary-200: #bae6fd;
    --color-primary-300: #7dd3fc;
    --color-primary-400: #38bdf8;
    --color-primary-500: #0ea5e9;
    --color-primary-600: #0284c7;
    --color-primary-700: #0369a1;
    --color-primary-800: #075985;
    --color-primary-900: #0c4a6e;

    /* Secondary colors */
    --color-secondary-50: #f8fafc;
    --color-secondary-100: #f1f5f9;
    --color-secondary-200: #e2e8f0;
    --color-secondary-300: #cbd5e1;
    --color-secondary-400: #94a3b8;
    --color-secondary-500: #64748b;
    --color-secondary-600: #475569;
    --color-secondary-700: #334155;
    --color-secondary-800: #1e293b;
    --color-secondary-900: #0f172a;

    /* Accent colors */
    --color-accent-50: #fdf4ff;
    --color-accent-100: #fae8ff;
    --color-accent-200: #f5d0fe;
    --color-accent-300: #f0abfc;
    --color-accent-400: #e879f9;
    --color-accent-500: #d946ef;
    --color-accent-600: #c026d3;
    --color-accent-700: #a21caf;
    --color-accent-800: #86198f;
    --color-accent-900: #701a75;
}

.theme-blue {
    --color-primary-50: #f0f9ff;
    --color-primary-100: #e0f2fe;
    --color-primary-200: #bae6fd;
    --color-primary-300: #7dd3fc;
    --color-primary-400: #38bdf8;
    --color-primary-500: #0ea5e9;
    --color-primary-600: #0284c7;
    --color-primary-700: #0369a1;
    --color-primary-800: #075985;
    --color-primary-900: #0c4a6e;

    --color-secondary-50: #f8fafc;
    --color-secondary-100: #f1f5f9;
    --color-secondary-200: #e2e8f0;
    --color-secondary-300: #cbd5e1;
    --color-secondary-400: #94a3b8;
    --color-secondary-500: #64748b;
    --color-secondary-600: #475569;
    --color-secondary-700: #334155;
    --color-secondary-800: #1e293b;
    --color-secondary-900: #0f172a;

    --color-accent-50: #eef2ff;
    --color-accent-100: #e0e7ff;
    --color-accent-200: #c7d2fe;
    --color-accent-300: #a5b4fc;
    --color-accent-400: #818cf8;
    --color-accent-500: #6366f1;
    --color-accent-600: #4f46e5;
    --color-accent-700: #4338ca;
    --color-accent-800: #3730a3;
    --color-accent-900: #312e81;
}

.theme-sass {
    --color-primary-50: #fdf2f8;
    --color-primary-100: #fce7f3;
    --color-primary-200: #fbcfe8;
    --color-primary-300: #f9a8d4;
    --color-primary-400: #f472b6;
    --color-primary-500: #cd5c8e;
    --color-primary-600: #bf4b8a;
    --color-primary-700: #9d3c72;
    --color-primary-800: #802d59;
    --color-primary-900: #621b3f;

    --color-secondary-50: #f8fafc;
    --color-secondary-100: #f1f5f9;
    --color-secondary-200: #e2e8f0;
    --color-secondary-300: #cbd5e1;
    --color-secondary-400: #94a3b8;
    --color-secondary-500: #64748b;
    --color-secondary-600: #475569;
    --color-secondary-700: #334155;
    --color-secondary-800: #1e293b;
    --color-secondary-900: #0f172a;

    --color-accent-50: #f5f3ff;
    --color-accent-100: #ede9fe;
    --color-accent-200: #ddd6fe;
    --color-accent-300: #c4b5fd;
    --color-accent-400: #a78bfa;
    --color-accent-500: #8b5cf6;
    --color-accent-600: #7c3aed;
    --color-accent-700: #6d28d9;
    --color-accent-800: #5b21b6;
    --color-accent-900: #4c1d95;
}

.theme-sass2 {
    /* Sage/Forest Green Primary */
    --color-primary-50: #f4f7f4;
    --color-primary-100: #e6ede6;
    --color-primary-200: #cfdecf;
    --color-primary-300: #a8c2a8;
    --color-primary-400: #7fa17f;
    --color-primary-500: #5c8a5c;
    --color-primary-600: #4a704a;
    --color-primary-700: #3d5c3d;
    --color-primary-800: #314831;
    --color-primary-900: #253825;

    /* Warm Gray Secondary */
    --color-secondary-50: #fafaf9;
    --color-secondary-100: #f5f5f4;
    --color-secondary-200: #e7e5e4;
    --color-secondary-300: #d6d3d1;
    --color-secondary-400: #a8a29e;
    --color-secondary-500: #78716c;
    --color-secondary-600: #57534e;
    --color-secondary-700: #44403c;
    --color-secondary-800: #292524;
    --color-secondary-900: #1c1917;

    /* Terracotta Accent */
    --color-accent-50: #fdf4f2;
    --color-accent-100: #fde8e4;
    --color-accent-200: #fad3cc;
    --color-accent-300: #f5b3a5;
    --color-accent-400: #e98970;
    --color-accent-500: #dc6547;
    --color-accent-600: #c54a2d;
    --color-accent-700: #a33b25;
    --color-accent-800: #863225;
    --color-accent-900: #6f2b22;
}

.theme-sass3 {
    /* Primary colors - Ocean Blues */
    --color-primary-50: #f0f9ff;
    --color-primary-100: #e0f7ff;
    --color-primary-200: #b9ecff;
    --color-primary-300: #7dd5f5;
    --color-primary-400: #38b7e8;
    --color-primary-500: #0c98d0;
    --color-primary-600: #0887c2;
    --color-primary-700: #0670a3;
    --color-primary-800: #065b86;
    --color-primary-900: #074563;

    /* Secondary colors - Sandy Neutrals */
    --color-secondary-50: #fdfcfb;
    --color-secondary-100: #f8f6f4;
    --color-secondary-200: #f0ece7;
    --color-secondary-300: #e2dcd4;
    --color-secondary-400: #cbc3b9;
    --color-secondary-500: #afa497;
    --color-secondary-600: #8c8278;
    --color-secondary-700: #6e665e;
    --color-secondary-800: #504b45;
    --color-secondary-900: #353230;

    /* Accent colors - Coral Sunset */
    --color-accent-50: #fff5f2;
    --color-accent-100: #ffe6e0;
    --color-accent-200: #ffc9bc;
    --color-accent-300: #ffa592;
    --color-accent-400: #ff7a61;
    --color-accent-500: #ff5341;
    --color-accent-600: #eb3a28;
    --color-accent-700: #d12a1a;
    --color-accent-800: #b22417;
    --color-accent-900: #8f1e15;
}

.theme-purple {
    --color-primary-50: #faf5ff;
    --color-primary-100: #f3e8ff;
    --color-primary-200: #e9d5ff;
    --color-primary-300: #d8b4fe;
    --color-primary-400: #c084fc;
    --color-primary-500: #a855f7;
    --color-primary-600: #9333ea;
    --color-primary-700: #7e22ce;
    --color-primary-800: #6b21a8;
    --color-primary-900: #581c87;

    --color-secondary-50: #fafafa;
    --color-secondary-100: #f4f4f5;
    --color-secondary-200: #e4e4e7;
    --color-secondary-300: #d4d4d8;
    --color-secondary-400: #a1a1aa;
    --color-secondary-500: #71717a;
    --color-secondary-600: #52525b;
    --color-secondary-700: #3f3f46;
    --color-secondary-800: #27272a;
    --color-secondary-900: #18181b;

    --color-accent-50: #fdf2f8;
    --color-accent-100: #fce7f3;
    --color-accent-200: #fbcfe8;
    --color-accent-300: #f9a8d4;
    --color-accent-400: #f472b6;
    --color-accent-500: #ec4899;
    --color-accent-600: #db2777;
    --color-accent-700: #be185d;
    --color-accent-800: #9d174d;
    --color-accent-900: #831843;
}

.theme-green {
    --color-primary-50: #f0fdf4;
    --color-primary-100: #dcfce7;
    --color-primary-200: #bbf7d0;
    --color-primary-300: #86efac;
    --color-primary-400: #4ade80;
    --color-primary-500: #22c55e;
    --color-primary-600: #16a34a;
    --color-primary-700: #15803d;
    --color-primary-800: #166534;
    --color-primary-900: #14532d;

    --color-secondary-50: #fafaf9;
    --color-secondary-100: #f5f5f4;
    --color-secondary-200: #e7e5e4;
    --color-secondary-300: #d6d3d1;
    --color-secondary-400: #a8a29e;
    --color-secondary-500: #78716c;
    --color-secondary-600: #57534e;
    --color-secondary-700: #44403c;
    --color-secondary-800: #292524;
    --color-secondary-900: #1c1917;

    --color-accent-50: #fffbeb;
    --color-accent-100: #fef3c7;
    --color-accent-200: #fde68a;
    --color-accent-300: #fcd34d;
    --color-accent-400: #fbbf24;
    --color-accent-500: #f59e0b;
    --color-accent-600: #d97706;
    --color-accent-700: #b45309;
    --color-accent-800: #92400e;
    --color-accent-900: #78350f;
}

.theme-finder {
    /* Primary colors - Orange (F78100) */
    --color-primary-50: #fff7ed;
    --color-primary-100: #ffedd5;
    --color-primary-200: #fed7aa;
    --color-primary-300: #fdba74;
    --color-primary-400: #f9a352;
    --color-primary-500: #F78100;
    --color-primary-600: #e67300;
    --color-primary-700: #cc6600;
    --color-primary-800: #a35200;
    --color-primary-900: #7a3e00;

    /* Secondary colors - Black/Gray */
    --color-secondary-50: #f9fafb;
    --color-secondary-100: #f3f4f6;
    --color-secondary-200: #e5e7eb;
    --color-secondary-300: #d1d5db;
    --color-secondary-400: #9ca3af;
    --color-secondary-500: #6b7280;
    --color-secondary-600: #4b5563;
    --color-secondary-700: #374151;
    --color-secondary-800: #1f2937;
    --color-secondary-900: #111827;

    /* Accent colors - Complementary to Orange */
    --color-accent-50: #f0f9ff;
    --color-accent-100: #e0f2fe;
    --color-accent-200: #bae6fd;
    --color-accent-300: #7dd3fc;
    --color-accent-400: #38bdf8;
    --color-accent-500: #0ea5e9;
    --color-accent-600: #0284c7;
    --color-accent-700: #0369a1;
    --color-accent-800: #075985;
    --color-accent-900: #0c4a6e;
}


@layer components {
    .btn-primary {
        @apply bg-primary-600 text-white hover:bg-primary-700 transition-colors;
    }

    .btn-secondary {
        @apply bg-secondary-200 text-secondary-800 hover:bg-secondary-300 transition-colors;
    }

    .input-primary {
        @apply border-secondary-300 focus:border-primary-500 focus:ring-primary-500;
    }
}


@layer base {
  :root {
        --background: 0 0% 100%;
        --foreground: 0 0% 3.9%;
        --card: 0 0% 100%;
        --card-foreground: 0 0% 3.9%;
        --popover: 0 0% 100%;
        --popover-foreground: 0 0% 3.9%;
        --primary: 0 0% 9%;
        --primary-foreground: 0 0% 98%;
        --secondary: 0 0% 96.1%;
        --secondary-foreground: 0 0% 9%;
        --muted: 0 0% 96.1%;
        --muted-foreground: 0 0% 45.1%;
        --accent: 0 0% 96.1%;
        --accent-foreground: 0 0% 9%;
        --destructive: 0 84.2% 60.2%;
        --destructive-foreground: 0 0% 98%;
        --border: 0 0% 89.8%;
        --input: 0 0% 89.8%;
        --ring: 0 0% 3.9%;
        --chart-1: 12 76% 61%;
        --chart-2: 173 58% 39%;
        --chart-3: 197 37% 24%;
        --chart-4: 43 74% 66%;
        --chart-5: 27 87% 67%;
        --radius: 0.5rem;
    }
  .dark {
        --background: 0 0% 3.9%;
        --foreground: 0 0% 98%;
        --card: 0 0% 3.9%;
        --card-foreground: 0 0% 98%;
        --popover: 0 0% 3.9%;
        --popover-foreground: 0 0% 98%;
        --primary: 0 0% 98%;
        --primary-foreground: 0 0% 9%;
        --secondary: 0 0% 14.9%;
        --secondary-foreground: 0 0% 98%;
        --muted: 0 0% 14.9%;
        --muted-foreground: 0 0% 63.9%;
        --accent: 0 0% 14.9%;
        --accent-foreground: 0 0% 98%;
        --destructive: 0 62.8% 30.6%;
        --destructive-foreground: 0 0% 98%;
        --border: 0 0% 14.9%;
        --input: 0 0% 14.9%;
        --ring: 0 0% 83.1%;
        --chart-1: 220 70% 50%;
        --chart-2: 160 60% 45%;
        --chart-3: 30 80% 55%;
        --chart-4: 280 65% 60%;
        --chart-5: 340 75% 55%;
    }
}


@layer base {
  * {
    @apply border-border;
    }
  body {
    @apply bg-background text-foreground;
    }
}
