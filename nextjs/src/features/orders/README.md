# User Orders & Notifications Feature

This feature provides comprehensive order tracking and notification functionality for users.

## Features Added

### 1. User Orders Management
- **My Orders Page** (`/my-orders`) - View all user orders with filtering and pagination
- **Order Detail Page** (`/my-orders/[id]`) - Detailed view of individual orders
- **Order Statistics** - Dashboard showing order counts and total spent
- **Order Status Tracking** - Visual indicators for order progress

### 2. Notifications System
- **Notification Dropdown** - Bell icon in header with unread count
- **Notifications Page** (`/notifications`) - Full notification management
- **Real-time Updates** - Automatic notifications for order status changes
- **Mark as Read** - Individual and bulk notification management

### 3. Navigation Integration
- Added "My Orders" and "Notifications" links to user dropdown menu
- Added notification bell icon to header for authenticated users
- Mobile-responsive navigation updates

## File Structure

```
src/features/orders/
├── api.ts                 # API functions for user orders
├── queries.ts             # TanStack React Query hooks
├── components/
│   ├── OrderCard.tsx      # Individual order card component
│   ├── OrderList.tsx      # Order list with filtering/pagination
│   ├── OrderStats.tsx     # Order statistics dashboard
│   └── index.ts           # Component exports
└── README.md              # This file

src/app/
├── my-orders/
│   ├── page.tsx           # My Orders main page
│   └── [id]/page.tsx      # Order detail page
└── notifications/
    └── page.tsx           # Notifications page

src/components/notifications/
└── NotificationDropdown.tsx # Header notification dropdown
```

## API Functions

### User Orders API (`src/features/orders/api.ts`)
- `getUserOrders()` - Get paginated user orders with filtering
- `getUserOrder(id)` - Get detailed order information
- `getUserOrderStats()` - Get order statistics for dashboard

### Notification Client Service (`src/lib/services/notification-client.ts`)
- `getUserNotifications()` - Get user notifications
- `markAsRead()` - Mark notification as read
- `markAllAsRead()` - Mark all notifications as read
- `getUnreadCount()` - Get count of unread notifications

## Components

### OrderCard
Displays individual order information including:
- Order ID and date
- Status badge with icon
- Total amount and item count
- Shipping address
- Link to order details

### OrderList
Provides order management interface with:
- Status filtering (All, Pending, Processing, Shipped, Delivered, Cancelled)
- Pagination
- Loading states
- Empty states

### OrderStats
Dashboard showing:
- Total orders count
- Pending orders count
- Completed orders count
- Total amount spent

### NotificationDropdown
Header dropdown showing:
- Unread notification count badge
- Recent notifications preview
- Mark as read functionality
- Link to full notifications page

## Order Status Flow

1. **Pending** - Order placed, payment being reviewed
2. **Processing** - Payment confirmed, order being prepared
3. **Shipped** - Order dispatched for delivery
4. **Delivered** - Order successfully delivered
5. **Cancelled** - Order cancelled
6. **Refunded** - Order refunded

## Notification Types

- **Order Status Updates** - When order status changes
- **Payment Confirmations** - When payments are processed
- **Shipping Updates** - When orders are shipped

## Usage

### Viewing Orders
```typescript
import { useUserOrders } from '@/features/orders/queries';

function MyComponent() {
  const { data, isLoading } = useUserOrders({
    page: 1,
    per_page: 10,
    status: 'pending'
  });
  
  return (
    <div>
      {data?.orders.map(order => (
        <OrderCard key={order.id} order={order} />
      ))}
    </div>
  );
}
```

### Getting Order Details
```typescript
import { useUserOrder } from '@/features/orders/queries';

function OrderDetail({ orderId }: { orderId: string }) {
  const { data: order, isLoading } = useUserOrder(orderId);
  
  if (isLoading) return <div>Loading...</div>;
  if (!order) return <div>Order not found</div>;
  
  return <div>{/* Order details */}</div>;
}
```

## Database Requirements

Ensure the following tables exist in your Supabase database:
- `orders` - Order information
- `order_items` - Items in each order
- `notifications` - User notifications
- `profiles` - User profiles
- `products` - Product information
- `stores` - Store information

## Styling

All components use Tailwind CSS and shadcn/ui components for consistent styling with the rest of the application.

## Mobile Responsive

All components are fully responsive and optimized for mobile devices.
