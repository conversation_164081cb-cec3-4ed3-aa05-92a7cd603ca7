import { createServerClient } from '@/lib/supabase/server';
import {
  Store,
  StoreFilterParams
} from './types';
import { MockDataService } from '@/lib/mock-data';

// Server-side API functions
export async function getStores(filters: StoreFilterParams = {}): Promise<Store[]> {
  try {
    // For now, use mock data
    const stores = await MockDataService.getStores(filters);
    return stores;

    // When ready to switch to real data:
    // const supabase = await createServerClient();
    // let query = supabase.from('stores').select('*');

    // if (filters.featured !== undefined) {
    //   query = query.eq('featured', filters.featured);
    // }

    // if (filters.search) {
    //   query = query.ilike('name', `%${filters.search}%`);
    // }

    // if (filters.sortBy) {
    //   query = query.order(filters.sortBy, { ascending: filters.sortOrder === 'asc' });
    // } else {
    //   query = query.order('name');
    // }

    // const { data, error } = await query;

    // if (error) throw error;
    // return data || [];
  } catch (error) {
    console.error('Error fetching stores:', error);
    return [];
  }
}

export async function getStoreBySlug(slug: string): Promise<Store | null> {
  try {
    // For now, use mock data
    return await MockDataService.getStoreBySlug(slug);

    // When ready to switch to real data:
    // const supabase = await createServerClient();
    // const { data, error } = await supabase
    //   .from('stores')
    //   .select('*')
    //   .eq('slug', slug)
    //   .single();

    // if (error) throw error;
    // return data;
  } catch (error) {
    console.error(`Error fetching store with slug ${slug}:`, error);
    return null;
  }
}

export async function getFeaturedStores(limit = 4): Promise<Store[]> {
  try {
    // For now, use mock data
    const stores = await MockDataService.getStores({ featured: true });
    return stores.slice(0, limit);

    // When ready to switch to real data:
    // const supabase = await createServerClient();
    // const { data, error } = await supabase
    //   .from('stores')
    //   .select('*')
    //   .eq('featured', true)
    //   .order('name')
    //   .limit(limit);

    // if (error) throw error;
    // return data || [];
  } catch (error) {
    console.error('Error fetching featured stores:', error);
    return [];
  }
}
