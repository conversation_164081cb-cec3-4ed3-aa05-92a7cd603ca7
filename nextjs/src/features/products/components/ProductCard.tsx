'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Heart, ShoppingCart } from 'lucide-react';
import { Product } from '../types';
import { formatCurrency } from '@/lib/utils';
import { useToast } from '@/lib/hooks/use-toast';
import { useCart } from '@/lib/context/CartContext';

interface ProductCardProps {
  product: Product;
  className?: string;
}

export function ProductCard({ product, className = '' }: ProductCardProps) {
  const { toast } = useToast();
  const { addToCart } = useCart();
  const {
    id,
    name,
    slug,
    price,
    compareAtPrice,
    currency,
    images,
    store,
    rating,
    reviewCount,
  } = product;

  const handleAddToCart = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    try {
      // Use the addToCart method from the cart context
      await addToCart(id, 1);
      toast({
        title: "Added to cart",
        description: `${name} has been added to your cart.`,
        variant: "success",
      });
    } catch (error) {
      console.error('Error adding to cart:', error);
      toast({
        title: "Error",
        description: "Failed to add item to cart. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Handle both mock data format and regular format
  const discount = compareAtPrice ? Math.round(((compareAtPrice - price) / compareAtPrice) * 100) : 0;

  // Handle different image formats (database vs mock data)
  let imageUrl = '/placeholder-product.jpg';

  // First try to get image from the images array (from product_images table)
  if (images && images.length > 0) {
    imageUrl = images[0].url;
  }
  // Then try the imageUrl property (mock data format)
  else if ('imageUrl' in product && product.imageUrl) {
    imageUrl = product.imageUrl as string;
  }
  // Then try image_url property (direct field in products table)
  else if ('image_url' in product && product.image_url) {
    imageUrl = product.image_url as string;
  }

  console.log('Product image data:', {
    productId: id,
    productName: name,
    hasImages: images && images.length > 0,
    imageUrl,
    rawImages: images
  });

  return (
    <div className={`group relative bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm hover:shadow-md transition-shadow ${className}`}>
      {/* Discount badge */}
      {discount > 0 && (
        <div className="absolute top-2 left-2 z-10 bg-red-500 text-white text-xs font-medium px-2 py-1 rounded">
          {discount}% OFF
        </div>
      )}

      {/* Wishlist button */}
      <button
        className="absolute top-2 right-2 z-10 bg-white/80 p-1.5 rounded-full text-gray-600 hover:text-red-500 transition-colors"
        aria-label="Add to wishlist"
      >
        <Heart className="w-4 h-4" />
      </button>

      {/* Product image */}
      <Link href={`/products/${slug}`} className="block relative h-48 md:h-56 lg:h-64 bg-gray-100">
        <Image
          src={imageUrl}
          alt={name}
          fill
          sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
          className="object-cover object-center group-hover:scale-105 transition-transform duration-300"
          priority
          onError={(e) => {
            // Fallback to placeholder if image fails to load
            const target = e.target as HTMLImageElement;
            target.src = '/placeholder-product.jpg';
          }}
        />
      </Link>

      {/* Product info */}
      <div className="p-4">
        {/* Store name if available */}
        {store && (
          <Link href={`/stores/${store.slug}`} className="text-xs text-gray-500 hover:text-primary-600 mb-1 block">
            {store.name}
          </Link>
        )}
        {/* Handle mock data store format */}
        {!store && 'storeId' in product && product.storeId && (
          <Link href={`/stores/${product.storeId}`} className="text-xs text-gray-500 hover:text-primary-600 mb-1 block">
            Store ID: {product.storeId}
          </Link>
        )}

        {/* Product name */}
        <Link href={`/products/${slug}`} className="block">
          <h3 className="text-sm font-medium text-gray-900 line-clamp-2 mb-1 hover:text-primary-600 transition-colors">
            {name}
          </h3>
        </Link>

        {/* Rating */}
        {rating > 0 && (
          <div className="flex items-center mb-1">
            <div className="flex items-center">
              {[...Array(5)].map((_, i) => (
                <svg
                  key={i}
                  className={`w-3 h-3 ${
                    i < Math.floor(rating) ? 'text-yellow-400' : 'text-gray-300'
                  }`}
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
              ))}
            </div>
            <span className="text-xs text-gray-500 ml-1">
              ({reviewCount})
            </span>
          </div>
        )}

        {/* Price */}
        <div className="flex items-center justify-between mt-2">
          <div>
            <span className="text-sm font-medium text-gray-900">
              {formatCurrency(price, currency)}
            </span>
            {compareAtPrice && compareAtPrice > price && (
              <span className="text-xs text-gray-500 line-through ml-1">
                {formatCurrency(compareAtPrice, currency)}
              </span>
            )}
          </div>

          {/* Add to cart button */}
          <button
            className="p-1.5 bg-primary-50 rounded-full text-primary-600 hover:bg-primary-100 transition-colors"
            aria-label="Add to cart"
            onClick={handleAddToCart}
          >
            <ShoppingCart className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
}
