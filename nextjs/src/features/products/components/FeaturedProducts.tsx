'use client';

import React from 'react';
import Link from 'next/link';
import { ArrowRight } from 'lucide-react';
import { ProductGrid } from './ProductGrid';
import { useFeaturedProducts } from '../queries';

interface FeaturedProductsProps {
  title: string;
  subtitle?: string;
  limit?: number;
  className?: string;
}

export function FeaturedProducts({
  title,
  subtitle,
  limit = 8,
  className = '',
}: FeaturedProductsProps) {
  const { data, isLoading, error } = useFeaturedProducts(limit);
  
  const products = data?.products?.data || [];

  return (
    <section className={`py-12 ${className}`}>
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">{title}</h2>
            {subtitle && <p className="mt-1 text-gray-500">{subtitle}</p>}
          </div>
          <Link
            href="/products?featured=true"
            className="mt-4 md:mt-0 inline-flex items-center text-primary-600 hover:text-primary-700 font-medium"
          >
            View all
            <ArrowRight className="ml-1 h-4 w-4" />
          </Link>
        </div>

        {isLoading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6 animate-pulse">
            {[...Array(limit)].map((_, index) => (
              <div key={index} className="bg-gray-200 rounded-lg h-72"></div>
            ))}
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <p className="text-red-500">Error loading products</p>
          </div>
        ) : (
          <ProductGrid products={products} />
        )}
      </div>
    </section>
  );
}
