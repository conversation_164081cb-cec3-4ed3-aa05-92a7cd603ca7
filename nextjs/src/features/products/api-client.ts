import {
  PaginationParams,
  ProductFilterParams,
  ProductsApiResponse,
  ProductApiResponse
} from './types';
import { createSPASassClient } from '@/lib/supabase/client';

// Client-side API functions
export async function getProductsClient(
  paginationParams: PaginationParams = { page: 1, limit: 10 },
  filterParams: ProductFilterParams = {}
): Promise<ProductsApiResponse> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    // Calculate pagination
    const { page, limit } = paginationParams;
    const from = (page - 1) * limit;
    const to = from + limit - 1;

    // Build query
    let query = supabase
      .from('products')
      .select(`
        *,
        category:categories(*),
        store:stores(*),
        images:product_images(*)
      `, { count: 'exact' })
      .range(from, to);

    // Apply filters
    if (filterParams.categoryId) {
      query = query.eq('category_id', filterParams.categoryId);
    }

    if (filterParams.storeId) {
      query = query.eq('store_id', filterParams.storeId);
    }

    if (filterParams.featured !== undefined) {
      query = query.eq('featured', filterParams.featured);
    }

    if (filterParams.trending !== undefined) {
      query = query.eq('trending', filterParams.trending);
    }

    if (filterParams.inStock !== undefined) {
      query = query.eq('in_stock', filterParams.inStock);
    }

    if (filterParams.search) {
      query = query.ilike('name', `%${filterParams.search}%`);
    }

    if (filterParams.minPrice !== undefined) {
      query = query.gte('price', filterParams.minPrice);
    }

    if (filterParams.maxPrice !== undefined) {
      query = query.lte('price', filterParams.maxPrice);
    }

    // Apply sorting
    const sortBy = filterParams.sortBy || 'created_at';
    const sortOrder = filterParams.sortOrder || 'desc';
    query = query.order(sortBy, { ascending: sortOrder === 'asc' });

    // Execute query
    const { data, error, count } = await query;

    if (error) throw error;

    // Format the response
    const totalPages = Math.ceil((count || 0) / limit);

    // Transform data to ensure inStock is properly set
    const transformedData = (data || []).map(product => ({
      ...product,
      // Always set products as in stock
      inStock: true
    }));

    return {
      products: {
        data: transformedData,
        meta: {
          total: count || 0,
          page,
          limit,
          totalPages
        }
      }
    };
  } catch (error) {
    console.error('Error fetching products:', error);
    return {
      products: {
        data: [],
        meta: {
          total: 0,
          page: paginationParams.page,
          limit: paginationParams.limit,
          totalPages: 0
        }
      },
      error: error instanceof Error ? error.message : 'An unknown error occurred'
    };
  }
}

export async function getProductBySlugClient(slug: string): Promise<ProductApiResponse> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    // Get product by slug
    const { data, error } = await supabase
      .from('products')
      .select(`
        *,
        category:categories(*),
        store:stores(*),
        images:product_images(*)
      `)
      .eq('slug', slug)
      .single();

    if (error) throw error;

    // Transform data to ensure inStock is properly set
    if (data) {
      const transformedProduct = {
        ...data,
        // Always set products as in stock
        inStock: true
      };
      return { product: transformedProduct };
    }

    return { product: null };
  } catch (error) {
    console.error(`Error fetching product with slug ${slug}:`, error);
    return {
      product: null,
      error: error instanceof Error ? error.message : 'An unknown error occurred'
    };
  }
}

export async function getFeaturedProductsClient(limit = 8): Promise<ProductsApiResponse> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    // Directly query for featured products to ensure we get all the data we need
    const { data, error, count } = await supabase
      .from('products')
      .select(`
        *,
        category:categories(*),
        store:stores(*),
        images:product_images(*)
      `, { count: 'exact' })
      .eq('featured', true)
      .limit(limit);

    if (error) throw error;

    console.log('Featured products data:', {
      count,
      productsCount: data?.length,
      firstProduct: data?.[0] ? {
        id: data[0].id,
        name: data[0].name,
        hasImages: data[0].images && data[0].images.length > 0,
        imageUrl: data[0].image_url,
        imagesCount: data[0].images?.length
      } : null
    });

    // Transform data to ensure inStock is properly set
    const transformedData = (data || []).map(product => ({
      ...product,
      // Always set products as in stock
      inStock: true
    }));

    return {
      products: {
        data: transformedData,
        meta: {
          total: count || 0,
          page: 1,
          limit,
          totalPages: Math.ceil((count || 0) / limit)
        }
      }
    };
  } catch (error) {
    console.error('Error fetching featured products:', error);
    return {
      products: {
        data: [],
        meta: {
          total: 0,
          page: 1,
          limit,
          totalPages: 0
        }
      },
      error: error instanceof Error ? error.message : 'An unknown error occurred'
    };
  }
}
