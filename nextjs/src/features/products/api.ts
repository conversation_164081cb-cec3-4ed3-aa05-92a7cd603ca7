import { createSPASassClient } from '@/lib/supabase/client';
import { createServerClient } from '@/lib/supabase/server';
import {
  Product,
  PaginationParams,
  ProductFilterParams,
  PaginatedResponse,
  ProductsApiResponse,
  ProductApiResponse
} from './types';
import { MockDataService } from '@/lib/mock-data';

// Server-side API functions
export async function getProducts(
  paginationParams: PaginationParams = { page: 1, limit: 10 },
  filterParams: ProductFilterParams = {}
): Promise<PaginatedResponse<Product>> {
  try {
    // For now, use mock data
    return await MockDataService.getProducts(paginationParams, filterParams);

    // When ready to switch to real data:
    // const supabase = await createServerClient();
    // const { data, error } = await supabase
    //   .from('products')
    //   .select('*')
    //   // Add filters, pagination, etc.

    // if (error) throw error;
    // return { data: data || [], meta: { ... } };
  } catch (error) {
    console.error('Error fetching products:', error);
    return {
      data: [],
      meta: {
        total: 0,
        page: paginationParams.page,
        limit: paginationParams.limit,
        totalPages: 0
      }
    };
  }
}

export async function getProductBySlug(slug: string): Promise<Product | null> {
  try {
    // For now, use mock data
    return await MockDataService.getProductBySlug(slug);

    // When ready to switch to real data:
    // const supabase = await createServerClient();
    // const { data, error } = await supabase
    //   .from('products')
    //   .select('*')
    //   .eq('slug', slug)
    //   .single();

    // if (error) throw error;
    // return data;
  } catch (error) {
    console.error(`Error fetching product with slug ${slug}:`, error);
    return null;
  }
}

export async function getFeaturedProducts(limit = 8): Promise<Product[]> {
  try {
    // For now, use mock data
    const response = await MockDataService.getProducts(
      { page: 1, limit },
      { featured: true }
    );
    return response.data;

    // When ready to switch to real data:
    // const supabase = await createServerClient();
    // const { data, error } = await supabase
    //   .from('products')
    //   .select('*')
    //   .eq('featured', true)
    //   .limit(limit);

    // if (error) throw error;
    // return data || [];
  } catch (error) {
    console.error('Error fetching featured products:', error);
    return [];
  }
}

// Client-side API functions
export async function getProductsClient(
  paginationParams: PaginationParams = { page: 1, limit: 10 },
  filterParams: ProductFilterParams = {}
): Promise<ProductsApiResponse> {
  try {
    // For now, use mock data
    const response = await MockDataService.getProducts(paginationParams, filterParams);
    return { products: response };

    // When ready to switch to real data:
    // const client = await createSPASassClient();
    // const { data, error } = await client.getProducts(paginationParams, filterParams);

    // if (error) throw error;
    // return { products: data };
  } catch (error) {
    console.error('Error fetching products:', error);
    return {
      products: {
        data: [],
        meta: {
          total: 0,
          page: paginationParams.page,
          limit: paginationParams.limit,
          totalPages: 0
        }
      },
      error: error instanceof Error ? error.message : 'An unknown error occurred'
    };
  }
}
