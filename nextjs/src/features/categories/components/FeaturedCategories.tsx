'use client';

import React from 'react';
import Link from 'next/link';
import { ArrowRight } from 'lucide-react';
import { CategoryGrid } from './CategoryGrid';
import { useFeaturedCategories } from '../queries';

interface FeaturedCategoriesProps {
  title: string;
  subtitle?: string;
  limit?: number;
  className?: string;
  variant?: 'default' | 'compact' | 'featured';
}

export function FeaturedCategories({
  title,
  subtitle,
  limit = 6,
  className = '',
  variant = 'featured',
}: FeaturedCategoriesProps) {
  const { data, isLoading, error } = useFeaturedCategories(limit);
  
  const categories = data?.categories || [];

  return (
    <section className={`py-12 ${className}`}>
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">{title}</h2>
            {subtitle && <p className="mt-1 text-gray-500">{subtitle}</p>}
          </div>
          <Link
            href="/categories"
            className="mt-4 md:mt-0 inline-flex items-center text-primary-600 hover:text-primary-700 font-medium"
          >
            View all
            <ArrowRight className="ml-1 h-4 w-4" />
          </Link>
        </div>

        {isLoading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 md:gap-6 animate-pulse">
            {[...Array(limit)].map((_, index) => (
              <div key={index} className="bg-gray-200 rounded-lg aspect-[16/9]"></div>
            ))}
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <p className="text-red-500">Error loading categories</p>
          </div>
        ) : (
          <CategoryGrid 
            categories={categories} 
            variant={variant}
            columns={variant === 'compact' ? 5 : 3}
          />
        )}
      </div>
    </section>
  );
}
