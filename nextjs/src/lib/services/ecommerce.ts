import { createServerClient } from '@/lib/supabase/server';
import { createSPASassClient } from '@/lib/supabase/client';
import {
  Product,
  Category,
  CartItem,
  WishlistItem,
  Order,
  Review
} from '@/lib/types/ecommerce';

export class EcommerceService {
  // Categories
  static async getCategories(): Promise<Category[]> {
    const supabase = await createServerClient();
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .order('name');

    if (error) throw error;
    return data || [];
  }

  static async getCategoryBySlug(slug: string): Promise<Category | null> {
    const supabase = await createServerClient();
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .eq('slug', slug)
      .single();

    if (error) throw error;
    return data;
  }

  // Products
  static async getProducts(options: {
    limit?: number;
    category_id?: number;
    featured?: boolean;
    search?: string;
    sort_by?: string;
    sort_order?: 'asc' | 'desc';
  } = {}): Promise<Product[]> {
    const {
      limit = 10,
      category_id,
      featured,
      search,
      sort_by = 'created_at',
      sort_order = 'desc'
    } = options;

    const supabase = await createServerClient();
    let query = supabase
      .from('products')
      .select(`
        *,
        category:categories(*),
        images:product_images(*)
      `)
      .order(sort_by, { ascending: sort_order === 'asc' })
      .limit(limit);

    if (category_id) {
      query = query.eq('category_id', category_id);
    }

    if (featured !== undefined) {
      query = query.eq('featured', featured);
    }

    if (search) {
      query = query.ilike('name', `%${search}%`);
    }

    const { data, error } = await query;

    if (error) throw error;
    return data || [];
  }

  static async getProductBySlug(slug: string): Promise<Product | null> {
    const supabase = await createServerClient();
    const { data, error } = await supabase
      .from('products')
      .select(`
        *,
        category:categories(*),
        images:product_images(*)
      `)
      .eq('slug', slug)
      .single();

    if (error) throw error;
    return data;
  }

  static async getFeaturedProducts(limit = 4): Promise<Product[]> {
    return this.getProducts({ featured: true, limit });
  }

  static async getRelatedProducts(productId: number, categoryId: number | null, limit = 4): Promise<Product[]> {
    const supabase = await createServerClient();
    let query = supabase
      .from('products')
      .select(`
        *,
        category:categories(*),
        images:product_images(*)
      `)
      .neq('id', productId)
      .limit(limit);

    if (categoryId) {
      query = query.eq('category_id', categoryId);
    }

    const { data, error } = await query;

    if (error) throw error;
    return data || [];
  }

  // Cart
  static async getCart(userId: string): Promise<CartItem[]> {
    const supabase = await createServerClient();
    const { data, error } = await supabase
      .from('cart')
      .select(`
        *,
        product:products(*)
      `)
      .eq('user_id', userId);

    if (error) throw error;
    return data || [];
  }

  static async addToCart(productId: number, quantity = 1, options: Record<string, any> | null = null): Promise<void> {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    const { data: user } = await supabase.auth.getUser();
    if (!user.user) throw new Error('User not authenticated');

    // Check if item already exists in cart
    const { data: existingItem } = await supabase
      .from('cart')
      .select('*')
      .eq('user_id', user.user.id)
      .eq('product_id', productId)
      .single();

    if (existingItem) {
      // Update quantity
      const { error } = await supabase
        .from('cart')
        .update({ quantity: existingItem.quantity + quantity, options })
        .eq('id', existingItem.id);

      if (error) throw error;
    } else {
      // Insert new item
      const { error } = await supabase
        .from('cart')
        .insert({
          user_id: user.user.id,
          product_id: productId,
          quantity,
          options
        });

      if (error) throw error;
    }
  }

  static async updateCartItem(cartItemId: number, quantity: number): Promise<void> {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    const { error } = await supabase
      .from('cart')
      .update({ quantity })
      .eq('id', cartItemId);

    if (error) throw error;
  }

  static async removeFromCart(cartItemId: number): Promise<void> {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    const { error } = await supabase
      .from('cart')
      .delete()
      .eq('id', cartItemId);

    if (error) throw error;
  }

  // Wishlist
  static async getWishlist(userId: string): Promise<WishlistItem[]> {
    const supabase = await createServerClient();
    const { data, error } = await supabase
      .from('wishlist')
      .select(`
        *,
        product:products(*)
      `)
      .eq('user_id', userId);

    if (error) throw error;
    return data || [];
  }

  static async addToWishlist(productId: number): Promise<void> {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    const { data: user } = await supabase.auth.getUser();
    if (!user.user) throw new Error('User not authenticated');

    const { error } = await supabase
      .from('wishlist')
      .insert({
        user_id: user.user.id,
        product_id: productId
      });

    if (error && error.code !== '23505') throw error; // Ignore unique constraint violations
  }

  static async removeFromWishlist(wishlistItemId: number): Promise<void> {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    const { error } = await supabase
      .from('wishlist')
      .delete()
      .eq('id', wishlistItemId);

    if (error) throw error;
  }

  // Orders
  static async getOrders(userId: string): Promise<Order[]> {
    const supabase = await createServerClient();
    const { data, error } = await supabase
      .from('orders')
      .select(`
        *,
        items:order_items(
          *,
          product:products(*)
        )
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }

  static async getOrderById(orderId: number, userId: string): Promise<Order | null> {
    const supabase = await createServerClient();
    const { data, error } = await supabase
      .from('orders')
      .select(`
        *,
        items:order_items(
          *,
          product:products(*)
        )
      `)
      .eq('id', orderId)
      .eq('user_id', userId)
      .single();

    if (error) throw error;
    return data;
  }

  // Reviews
  static async getProductReviews(productId: number): Promise<Review[]> {
    const supabase = await createServerClient();
    const { data, error } = await supabase
      .from('reviews')
      .select(`
        *,
        user:auth.users(email)
      `)
      .eq('product_id', productId)
      .eq('status', 'approved')
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }

  static async addReview(
    productId: number,
    rating: number,
    title: string | null = null,
    content: string | null = null
  ): Promise<void> {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    const { data: user } = await supabase.auth.getUser();
    if (!user.user) throw new Error('User not authenticated');

    const { error } = await supabase
      .from('reviews')
      .insert({
        user_id: user.user.id,
        product_id: productId,
        rating,
        title,
        content
      });

    if (error) throw error;
  }
}
