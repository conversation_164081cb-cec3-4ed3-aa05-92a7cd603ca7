import React from 'react';
import { createSPASassClient } from '@/lib/supabase/client';
import { FeatureToggle, FeatureKey, UserRole } from '@/lib/types/roles';

export class FeatureToggleService {
  private static instance: FeatureToggleService;
  private toggles: Map<string, FeatureToggle> = new Map();
  private initialized = false;

  static getInstance(): FeatureToggleService {
    if (!FeatureToggleService.instance) {
      FeatureToggleService.instance = new FeatureToggleService();
    }
    return FeatureToggleService.instance;
  }

  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      const client = await createSPASassClient();
      const supabase = client.getSupabaseClient();

      const { data: toggles, error } = await supabase
        .from('feature_toggles')
        .select('*')
        .order('feature_key');

      if (error) {
        console.warn('Feature toggles table not found or error loading, using defaults:', error.message);
        // Initialize with default toggles if database fails
        this.initializeDefaults();
        return;
      }

      if (toggles && toggles.length > 0) {
        toggles.forEach(toggle => {
          this.toggles.set(toggle.feature_key, toggle);
        });
        this.initialized = true;
      } else {
        console.warn('No feature toggles found in database, using defaults');
        this.initializeDefaults();
      }
    } catch (error) {
      console.warn('Error initializing feature toggles, using defaults:', error);
      this.initializeDefaults();
    }
  }

  private initializeDefaults(): void {
    const defaultToggles: Partial<FeatureToggle>[] = [
      {
        feature_key: 'store_management',
        feature_name: 'Store Management',
        description: 'Access to store creation and management',
        enabled: true,
        role_restrictions: ['admin']
      },
      {
        feature_key: 'product_management',
        feature_name: 'Product Management',
        description: 'Access to product creation and management',
        enabled: true,
        role_restrictions: ['admin', 'store_owner']
      },
      {
        feature_key: 'order_management',
        feature_name: 'Order Management',
        description: 'Access to order processing and management',
        enabled: true,
        role_restrictions: ['admin', 'store_owner']
      },
      {
        feature_key: 'payout_management',
        feature_name: 'Payout Management',
        description: 'Access to payout processing and management',
        enabled: true,
        role_restrictions: ['admin', 'store_owner']
      },
      {
        feature_key: 'analytics_view',
        feature_name: 'Analytics View',
        description: 'Access to analytics and reporting',
        enabled: true,
        role_restrictions: ['admin', 'store_owner']
      },
      {
        feature_key: 'user_management',
        feature_name: 'User Management',
        description: 'Access to user management',
        enabled: true,
        role_restrictions: ['admin']
      },
      {
        feature_key: 'settings_access',
        feature_name: 'Settings Access',
        description: 'Access to system settings',
        enabled: true,
        role_restrictions: ['admin', 'store_owner']
      }
    ];

    defaultToggles.forEach(toggle => {
      this.toggles.set(toggle.feature_key!, toggle as FeatureToggle);
    });

    this.initialized = true;
  }

  isFeatureEnabled(featureKey: FeatureKey, userRole: UserRole): boolean {
    // If not initialized, use safe defaults based on role
    if (!this.initialized) {
      return this.getDefaultFeatureAccess(featureKey, userRole);
    }

    const toggle = this.toggles.get(featureKey);
    if (!toggle) {
      console.warn(`Feature toggle not found for key: ${featureKey}, using default`);
      return this.getDefaultFeatureAccess(featureKey, userRole);
    }

    // If feature toggle is disabled, fall back to default role-based access
    // This ensures that when toggles are turned off, users still get basic access
    if (!toggle.enabled) {
      return this.getDefaultFeatureAccess(featureKey, userRole);
    }

    // Check role restrictions when feature is enabled
    if (toggle.role_restrictions && toggle.role_restrictions.length > 0) {
      return toggle.role_restrictions.includes(userRole);
    }

    return true;
  }

  private getDefaultFeatureAccess(featureKey: FeatureKey, userRole: UserRole): boolean {
    // Safe defaults when feature toggles are not available
    const defaultAccess: Record<FeatureKey, UserRole[]> = {
      'store_management': ['admin'],
      'product_management': ['admin', 'store_owner'],
      'order_management': ['admin', 'store_owner'], // Store owners can view orders from their stores
      'payout_management': ['admin'], // Only admins handle payouts
      'analytics_view': ['admin', 'store_owner'],
      'user_management': ['admin'],
      'settings_access': ['admin'] // Only admins access settings
    };

    return defaultAccess[featureKey]?.includes(userRole) ?? false;
  }

  async updateFeatureToggle(featureKey: FeatureKey, enabled: boolean, roleRestrictions?: UserRole[]): Promise<boolean> {
    try {
      const client = await createSPASassClient();
      const supabase = client.getSupabaseClient();

      const updateData: Partial<FeatureToggle> = {
        enabled,
        role_restrictions: roleRestrictions,
        updated_at: new Date().toISOString()
      };

      const { error } = await supabase
        .from('feature_toggles')
        .update(updateData)
        .eq('feature_key', featureKey);

      if (error) {
        console.error('Error updating feature toggle:', error);
        return false;
      }

      // Update local cache
      const existingToggle = this.toggles.get(featureKey);
      if (existingToggle) {
        this.toggles.set(featureKey, {
          ...existingToggle,
          ...updateData
        } as FeatureToggle);
      }

      return true;
    } catch (error) {
      console.error('Error updating feature toggle:', error);
      return false;
    }
  }

  getAllToggles(): FeatureToggle[] {
    return Array.from(this.toggles.values());
  }

  async refreshToggles(): Promise<void> {
    this.initialized = false;
    this.toggles.clear();
    await this.initialize();
  }
}

// Export singleton instance
export const featureToggleService = FeatureToggleService.getInstance();

// Hook for React components
export function useFeatureToggle(featureKey: FeatureKey, userRole: UserRole) {
  const [isEnabled, setIsEnabled] = React.useState(false);
  const [loading, setLoading] = React.useState(true);

  React.useEffect(() => {
    const checkFeature = async () => {
      await featureToggleService.initialize();
      const enabled = featureToggleService.isFeatureEnabled(featureKey, userRole);
      setIsEnabled(enabled);
      setLoading(false);
    };

    checkFeature();
  }, [featureKey, userRole]);

  return { isEnabled, loading };
}
