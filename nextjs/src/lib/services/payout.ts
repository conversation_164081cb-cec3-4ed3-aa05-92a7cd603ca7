import { createSPASassClient } from '@/lib/supabase/client';

export interface PayoutSchedule {
  id: string;
  store_id: string;
  payout_date: string;
  total_amount: number;
  service_fees_deducted: number;
  net_amount: number;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  created_at: string;
  updated_at: string;
}

export interface StorePayoutSummary {
  totalPendingAmount: number;
  nextPayoutDate: string | null;
  upcomingPayouts: PayoutSchedule[];
  totalSales: number;
  confirmedSales: number;
}

export class PayoutService {
  /**
   * Get payout schedule for a specific store
   */
  static async getStorePayoutSchedule(storeId: string): Promise<StorePayoutSummary> {
    try {
      const client = await createSPASassClient();
      const supabase = client.getSupabaseClient();

      // Get upcoming payouts for the store
      const { data: payouts, error: payoutsError } = await supabase
        .from('payout_schedule')
        .select('*')
        .eq('store_id', storeId)
        .gte('payout_date', new Date().toISOString().split('T')[0]) // Only future payouts
        .order('payout_date', { ascending: true });

      if (payoutsError) throw payoutsError;

      // Calculate totals
      const totalPendingAmount = payouts?.reduce((sum, payout) => {
        return sum + (payout.status === 'pending' ? payout.net_amount : 0);
      }, 0) || 0;

      const nextPayoutDate = payouts?.[0]?.payout_date || null;

      // Get total sales for the store
      const { data: salesData, error: salesError } = await supabase
        .from('order_items')
        .select('price, quantity')
        .eq('store_id', storeId);

      if (salesError) throw salesError;

      const totalSales = salesData?.reduce((sum, item) => {
        return sum + (item.price * item.quantity);
      }, 0) || 0;

      // Get confirmed sales (from confirmed payments)
      const { data: confirmedSalesData, error: confirmedSalesError } = await supabase
        .from('order_items')
        .select(`
          price,
          quantity,
          order_id,
          orders!inner(
            id,
            confirmed_payments!inner(
              id
            )
          )
        `)
        .eq('store_id', storeId);

      if (confirmedSalesError) throw confirmedSalesError;

      const confirmedSales = confirmedSalesData?.reduce((sum, item) => {
        return sum + (item.price * item.quantity);
      }, 0) || 0;

      return {
        totalPendingAmount,
        nextPayoutDate,
        upcomingPayouts: payouts || [],
        totalSales,
        confirmedSales
      };
    } catch (error) {
      console.error('Error getting store payout schedule:', error);
      return {
        totalPendingAmount: 0,
        nextPayoutDate: null,
        upcomingPayouts: [],
        totalSales: 0,
        confirmedSales: 0
      };
    }
  }

  /**
   * Calculate service fees for an amount
   */
  static calculateServiceFee(amount: number): number {
    return amount > 201 ? 5.00 : 2.00;
  }

  /**
   * Update payout schedule amounts based on confirmed payments
   */
  static async updatePayoutSchedule(storeId: string, amount: number, serviceFee: number): Promise<boolean> {
    try {
      const client = await createSPASassClient();
      const supabase = client.getSupabaseClient();

      // Get the next payout date (Monday or Friday)
      const nextPayoutDate = this.getNextPayoutDate();

      // Check if payout schedule exists for this date
      const { data: existingPayout, error: checkError } = await supabase
        .from('payout_schedule')
        .select('*')
        .eq('store_id', storeId)
        .eq('payout_date', nextPayoutDate)
        .single();

      if (checkError && checkError.code !== 'PGRST116') {
        throw checkError;
      }

      if (existingPayout) {
        // Update existing payout
        const { error: updateError } = await supabase
          .from('payout_schedule')
          .update({
            total_amount: existingPayout.total_amount + amount,
            service_fees_deducted: existingPayout.service_fees_deducted + serviceFee,
            net_amount: existingPayout.net_amount + (amount - serviceFee),
            updated_at: new Date().toISOString()
          })
          .eq('id', existingPayout.id);

        if (updateError) throw updateError;
      } else {
        // Create new payout schedule
        const { error: insertError } = await supabase
          .from('payout_schedule')
          .insert({
            store_id: storeId,
            payout_date: nextPayoutDate,
            total_amount: amount,
            service_fees_deducted: serviceFee,
            net_amount: amount - serviceFee,
            status: 'pending'
          });

        if (insertError) throw insertError;
      }

      return true;
    } catch (error) {
      console.error('Error updating payout schedule:', error);
      return false;
    }
  }

  /**
   * Get the next payout date (Monday or Friday)
   */
  private static getNextPayoutDate(): string {
    const today = new Date();
    const dayOfWeek = today.getDay(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday

    let nextPayoutDate: Date;

    if (dayOfWeek === 0 || dayOfWeek === 6 || dayOfWeek === 7) {
      // Weekend - next payout is Monday
      const daysUntilMonday = dayOfWeek === 0 ? 1 : 2;
      nextPayoutDate = new Date(today);
      nextPayoutDate.setDate(today.getDate() + daysUntilMonday);
    } else if (dayOfWeek >= 1 && dayOfWeek <= 3) {
      // Monday to Wednesday - next payout is Friday
      const daysUntilFriday = 5 - dayOfWeek;
      nextPayoutDate = new Date(today);
      nextPayoutDate.setDate(today.getDate() + daysUntilFriday);
    } else {
      // Thursday or Friday - next payout is next Monday
      const daysUntilNextMonday = dayOfWeek === 4 ? 4 : 3;
      nextPayoutDate = new Date(today);
      nextPayoutDate.setDate(today.getDate() + daysUntilNextMonday);
    }

    return nextPayoutDate.toISOString().split('T')[0];
  }

  /**
   * Generate payout schedules for all active stores
   */
  static async generatePayoutSchedules(): Promise<boolean> {
    try {
      const client = await createSPASassClient();
      const supabase = client.getSupabaseClient();

      // Call the database function to generate payout schedules
      const { error } = await supabase.rpc('generate_payout_schedule');

      if (error) throw error;

      return true;
    } catch (error) {
      console.error('Error generating payout schedules:', error);
      return false;
    }
  }
}
