import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function generateRandomString(length = 8, charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789') {
  let result = '';
  const charsetLength = charset.length;

  for (let i = 0; i < length; i++) {
    result += charset.charAt(Math.floor(Math.random() * charsetLength));
  }

  return result;
}

/**
 * Format a number as currency
 * @param amount - The amount to format
 * @param currency - The currency code (default: 'GMD' for Gambian Dalasi)
 * @returns Formatted currency string
 */
export function formatCurrency(amount: number, currency: string = 'GMD'): string {
  // Currency formatting options
  const options: Intl.NumberFormatOptions = {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  };

  // Special handling for Gambian Dalasi which might not be supported in all browsers
  if (currency === 'GMD') {
    return `D${amount.toFixed(2)}`;
  }

  // Use Intl.NumberFormat for other currencies
  return new Intl.NumberFormat('en-US', options).format(amount);
}