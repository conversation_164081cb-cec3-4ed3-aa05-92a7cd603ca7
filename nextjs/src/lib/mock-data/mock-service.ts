import { products } from './products';
import { categories } from './categories';
import { stores } from './stores';
import {
  Product,
  Category,
  Store,
  PaginationParams,
  PaginatedResponse,
  FilterParams,
  CartItem,
  Order,
  Review,
  Wishlist
} from './types';

// Helper function to simulate API delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Helper function to paginate results
function paginate<T>(items: T[], { page, limit }: PaginationParams): PaginatedResponse<T> {
  const startIndex = (page - 1) * limit;
  const endIndex = page * limit;
  const data = items.slice(startIndex, endIndex);

  return {
    data,
    meta: {
      total: items.length,
      page,
      limit,
      totalPages: Math.ceil(items.length / limit)
    }
  };
}

// Helper function to filter products
function filterProducts(items: Product[], filters: FilterParams): Product[] {
  return items.filter(item => {
    // Search filter
    if (filters.search && !item.name.toLowerCase().includes(filters.search.toLowerCase()) &&
        !item.description.toLowerCase().includes(filters.search.toLowerCase())) {
      return false;
    }

    // Category filter
    if (filters.categoryId && item.categoryId !== filters.categoryId) {
      return false;
    }

    // Store filter
    if (filters.storeId && item.storeId !== filters.storeId) {
      return false;
    }

    // Featured filter
    if (filters.featured !== undefined && item.featured !== filters.featured) {
      return false;
    }

    // In stock filter
    if (filters.inStock !== undefined && item.inStock !== filters.inStock) {
      return false;
    }

    // Price range filter
    if (filters.minPrice !== undefined && item.price < filters.minPrice) {
      return false;
    }

    if (filters.maxPrice !== undefined && item.price > filters.maxPrice) {
      return false;
    }

    return true;
  });
}

// Mock data service
export const MockDataService = {
  // Products
  async getProducts(
    paginationParams: PaginationParams = { page: 1, limit: 10 },
    filterParams: FilterParams = {}
  ): Promise<PaginatedResponse<Product>> {
    await delay(300); // Simulate API delay

    const filteredProducts = filterProducts(products, filterParams);

    // Sort products if needed
    if (filterParams.sortBy) {
      filteredProducts.sort((a, b) => {
        const aValue = a[filterParams.sortBy as keyof Product];
        const bValue = b[filterParams.sortBy as keyof Product];

        if (typeof aValue === 'string' && typeof bValue === 'string') {
          return filterParams.sortOrder === 'desc'
            ? bValue.localeCompare(aValue)
            : aValue.localeCompare(bValue);
        }

        if (typeof aValue === 'number' && typeof bValue === 'number') {
          return filterParams.sortOrder === 'desc'
            ? bValue - aValue
            : aValue - bValue;
        }

        return 0;
      });
    }

    // Convert the mock products to the expected format
    const formattedProducts = filteredProducts.map(product => ({
      ...product,
      slug: product.name.toLowerCase().replace(/\s+/g, '-'),
      currency: 'GMD',
      images: [{ id: '1', productId: product.id, url: product.imageUrl, alt: product.name, position: 1 }],
      rating: 4.5,
      reviewCount: 10,
      // Add store information
      store: stores.find(store => store.id === product.storeId) ? {
        id: product.storeId,
        name: stores.find(store => store.id === product.storeId)?.name || 'Unknown Store',
        slug: stores.find(store => store.id === product.storeId)?.slug || 'unknown-store',
      } : undefined,
      // Add category information
      category: categories.find(category => category.id === product.categoryId) ? {
        id: product.categoryId,
        name: categories.find(category => category.id === product.categoryId)?.name || 'Unknown Category',
        slug: categories.find(category => category.id === product.categoryId)?.slug || 'unknown-category',
      } : undefined,
    }));

    return paginate(formattedProducts, paginationParams);
  },

  async getProductById(id: string): Promise<Product | null> {
    await delay(200);
    return products.find(product => product.id === id) || null;
  },

  async getProductBySlug(slug: string): Promise<Product | null> {
    await delay(200);
    // Create slug from product name if it doesn't exist
    const product = products.find(product => {
      // If the product has a slug property, use it
      if ('slug' in product) {
        return product.slug === slug;
      }
      // Otherwise, create a slug from the name
      const generatedSlug = product.name.toLowerCase().replace(/\s+/g, '-');
      return generatedSlug === slug;
    });

    if (!product) return null;

    // Convert the mock product to the expected format
    return {
      ...product,
      slug: slug,
      currency: 'GMD',
      images: [{ id: '1', productId: product.id, url: product.imageUrl, alt: product.name, position: 1 }],
      rating: 4.5,
      reviewCount: 10,
      // Add store information
      store: stores.find(store => store.id === product.storeId) ? {
        id: product.storeId,
        name: stores.find(store => store.id === product.storeId)?.name || 'Unknown Store',
        slug: stores.find(store => store.id === product.storeId)?.slug || 'unknown-store',
      } : undefined,
      // Add category information
      category: categories.find(category => category.id === product.categoryId) ? {
        id: product.categoryId,
        name: categories.find(category => category.id === product.categoryId)?.name || 'Unknown Category',
        slug: categories.find(category => category.id === product.categoryId)?.slug || 'unknown-category',
      } : undefined,
    };
  },

  async getFeaturedProducts(limit: number = 6): Promise<Product[]> {
    await delay(200);
    const featuredProducts = products.filter(product => product.featured).slice(0, limit);

    // Convert the mock products to the expected format
    return featuredProducts.map(product => ({
      ...product,
      slug: product.name.toLowerCase().replace(/\s+/g, '-'),
      currency: 'GMD',
      images: [{ id: '1', productId: product.id, url: product.imageUrl, alt: product.name, position: 1 }],
      rating: 4.5,
      reviewCount: 10,
      // Add store information
      store: stores.find(store => store.id === product.storeId) ? {
        id: product.storeId,
        name: stores.find(store => store.id === product.storeId)?.name || 'Unknown Store',
        slug: stores.find(store => store.id === product.storeId)?.slug || 'unknown-store',
      } : undefined,
      // Add category information
      category: categories.find(category => category.id === product.categoryId) ? {
        id: product.categoryId,
        name: categories.find(category => category.id === product.categoryId)?.name || 'Unknown Category',
        slug: categories.find(category => category.id === product.categoryId)?.slug || 'unknown-category',
      } : undefined,
    }));
  },

  async getRelatedProducts(productId: string, limit: number = 4): Promise<Product[]> {
    await delay(200);
    const product = products.find(p => p.id === productId);
    if (!product) return [];

    const relatedProducts = products
      .filter(p => p.id !== productId && p.categoryId === product.categoryId)
      .slice(0, limit);

    // Convert the mock products to the expected format
    return relatedProducts.map(product => ({
      ...product,
      slug: product.name.toLowerCase().replace(/\s+/g, '-'),
      currency: 'GMD',
      images: [{ id: '1', productId: product.id, url: product.imageUrl, alt: product.name, position: 1 }],
      rating: 4.5,
      reviewCount: 10,
      // Add store information
      store: stores.find(store => store.id === product.storeId) ? {
        id: product.storeId,
        name: stores.find(store => store.id === product.storeId)?.name || 'Unknown Store',
        slug: stores.find(store => store.id === product.storeId)?.slug || 'unknown-store',
      } : undefined,
      // Add category information
      category: categories.find(category => category.id === product.categoryId) ? {
        id: product.categoryId,
        name: categories.find(category => category.id === product.categoryId)?.name || 'Unknown Category',
        slug: categories.find(category => category.id === product.categoryId)?.slug || 'unknown-category',
      } : undefined,
    }));
  },

  // Categories
  async getCategories(): Promise<Category[]> {
    await delay(200);

    // Convert the mock categories to the expected format
    return categories.map(category => ({
      ...category,
      image: category.imageUrl,
      productCount: products.filter(product => product.categoryId === category.id).length
    }));
  },

  async getCategoryById(id: string): Promise<Category | null> {
    await delay(200);
    const category = categories.find(category => category.id === id);
    if (!category) return null;

    // Convert the mock category to the expected format
    return {
      ...category,
      image: category.imageUrl,
      productCount: products.filter(product => product.categoryId === category.id).length
    };
  },

  async getCategoryBySlug(slug: string): Promise<Category | null> {
    await delay(200);
    const category = categories.find(category => category.slug === slug);
    if (!category) return null;

    // Convert the mock category to the expected format
    return {
      ...category,
      image: category.imageUrl,
      productCount: products.filter(product => product.categoryId === category.id).length
    };
  },

  async getFeaturedCategories(limit: number = 4): Promise<Category[]> {
    await delay(200);
    const featuredCategories = categories.filter(category => category.featured).slice(0, limit);

    // Convert the mock categories to the expected format
    return featuredCategories.map(category => ({
      ...category,
      image: category.imageUrl,
      productCount: products.filter(product => product.categoryId === category.id).length
    }));
  },

  // Stores
  async getStores(): Promise<Store[]> {
    await delay(200);
    // Map the mock store data to match the expected Store interface
    return stores.map(store => ({
      ...store,
      logo: store.logoUrl,
      coverImage: store.coverImageUrl,
      rating: 4.5, // Add some default values
      reviewCount: 10,
      productCount: 15
    }));
  },

  async getStoreById(id: string): Promise<Store | null> {
    await delay(200);
    const store = stores.find(store => store.id === id);
    if (!store) return null;

    // Map the mock store data to match the expected Store interface
    return {
      ...store,
      logo: store.logoUrl,
      coverImage: store.coverImageUrl,
      rating: 4.5, // Add some default values
      reviewCount: 10,
      productCount: 15
    };
  },

  async getFeaturedStores(limit: number = 4): Promise<Store[]> {
    await delay(200);
    // Filter featured stores and map them to match the expected Store interface
    return stores
      .filter(store => store.featured)
      .slice(0, limit)
      .map(store => ({
        ...store,
        logo: store.logoUrl,
        coverImage: store.coverImageUrl,
        rating: 4.5, // Add some default values
        reviewCount: 10,
        productCount: 15
      }));
  },

  // Cart
  async getCart(userId: string): Promise<CartItem[]> {
    await delay(200);
    // This would be fetched from a database in a real app
    // For now, return an empty array
    return [];
  },

  async addToCart(userId: string, productId: string, quantity: number = 1): Promise<CartItem> {
    await delay(300);
    // This would add to a database in a real app
    // For now, return a mock cart item
    const product = products.find(p => p.id === productId);
    if (!product) {
      throw new Error('Product not found');
    }

    return {
      id: `cart-${Date.now()}`,
      userId,
      productId,
      quantity,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      product
    };
  },

  // Orders
  async getOrders(userId: string): Promise<Order[]> {
    await delay(300);
    // This would be fetched from a database in a real app
    // For now, return an empty array
    return [];
  },

  // Reviews
  async getProductReviews(productId: string): Promise<Review[]> {
    await delay(200);
    // This would be fetched from a database in a real app
    // For now, return an empty array
    return [];
  },

  // Wishlist
  async getWishlist(userId: string): Promise<Wishlist[]> {
    await delay(200);
    // This would be fetched from a database in a real app
    // For now, return an empty array
    return [];
  }
};
