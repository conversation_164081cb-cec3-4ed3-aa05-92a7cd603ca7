import { Product } from './types';

// Mock products data
export const products: Product[] = [
  {
    id: 'prod-1',
    name: 'Smartphone X',
    description: 'Latest smartphone with advanced features',
    price: 699.99,
    imageUrl: 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1160&q=80',
    categoryId: 'cat-1', // Electronics
    storeId: 'store-1', // TechHub
    featured: true,
    inStock: true,
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z'
  },
  {
    id: 'prod-2',
    name: 'Laptop Pro',
    description: 'Powerful laptop for professionals',
    price: 1299.99,
    salePrice: 999.99,
    imageUrl: 'https://images.unsplash.com/photo-1496181133206-80ce9b88a853?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1471&q=80',
    categoryId: 'cat-1', // Electronics
    storeId: 'store-1', // TechHub
    featured: true,
    inStock: true,
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z'
  },
  {
    id: 'prod-3',
    name: 'Wireless Earbuds',
    description: 'High-quality wireless earbuds with noise cancellation',
    price: 149.99,
    imageUrl: 'https://images.unsplash.com/photo-1590658268037-6bf12165a8df?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1632&q=80',
    categoryId: 'cat-1', // Electronics
    storeId: 'store-1', // TechHub
    featured: false,
    inStock: true,
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z'
  },
  {
    id: 'prod-4',
    name: 'Designer T-Shirt',
    description: 'Premium cotton t-shirt with modern design',
    price: 39.99,
    imageUrl: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1480&q=80',
    categoryId: 'cat-2', // Clothing
    storeId: 'store-2', // Fashion Forward
    featured: true,
    inStock: true,
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z'
  },
  {
    id: 'prod-5',
    name: 'Slim Fit Jeans',
    description: 'Comfortable slim fit jeans for everyday wear',
    price: 59.99,
    salePrice: 39.99,
    imageUrl: 'https://images.unsplash.com/photo-1542272604-787c3835535d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1026&q=80',
    categoryId: 'cat-2', // Clothing
    storeId: 'store-2', // Fashion Forward
    featured: false,
    inStock: true,
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z'
  },
  {
    id: 'prod-6',
    name: 'Smart Watch',
    description: 'Track your fitness and stay connected',
    price: 199.99,
    salePrice: 149.99,
    imageUrl: 'https://images.unsplash.com/photo-1579586337278-3befd40fd17a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1472&q=80',
    categoryId: 'cat-1', // Electronics
    storeId: 'store-1', // TechHub
    featured: true,
    inStock: true,
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z'
  },
  {
    id: 'prod-7',
    name: 'Coffee Maker',
    description: 'Brew perfect coffee every morning',
    price: 89.99,
    imageUrl: 'https://images.unsplash.com/photo-1517668808822-9ebb02f2a0e6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
    categoryId: 'cat-3', // Home & Kitchen
    storeId: 'store-3', // Home Essentials
    featured: true,
    inStock: true,
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z'
  },
  {
    id: 'prod-8',
    name: 'Blender',
    description: 'Powerful blender for smoothies and more',
    price: 69.99,
    imageUrl: 'https://images.unsplash.com/photo-1590794056226-79ef3a8147e1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1471&q=80',
    categoryId: 'cat-3', // Home & Kitchen
    storeId: 'store-3', // Home Essentials
    featured: false,
    inStock: true,
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z'
  },
  {
    id: 'prod-9',
    name: 'Skincare Set',
    description: 'Complete skincare routine in one set',
    price: 79.99,
    salePrice: 59.99,
    imageUrl: 'https://images.unsplash.com/photo-**********-195a672e8a03?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1374&q=80',
    categoryId: 'cat-4', // Beauty & Health
    storeId: 'store-4', // Beauty Box
    featured: true,
    inStock: true,
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z'
  },
  {
    id: 'prod-10',
    name: 'Yoga Mat',
    description: 'Non-slip yoga mat for your practice',
    price: 29.99,
    imageUrl: 'https://images.unsplash.com/photo-1592432678016-e910b452f9a2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
    categoryId: 'cat-5', // Sports & Outdoors
    storeId: 'store-5', // Sports World
    featured: false,
    inStock: true,
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z'
  },
  {
    id: 'prod-11',
    name: 'Board Game',
    description: 'Fun strategy game for the whole family',
    price: 34.99,
    imageUrl: 'https://images.unsplash.com/photo-1610890716171-6b1bb98ffd09?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1631&q=80',
    categoryId: 'cat-6', // Toys & Games
    storeId: 'store-6', // Toy Land
    featured: true,
    inStock: true,
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z'
  },
  {
    id: 'prod-12',
    name: 'Bestselling Novel',
    description: 'The latest bestseller everyone is talking about',
    price: 19.99,
    imageUrl: 'https://images.unsplash.com/photo-1544947950-fa07a98d237f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1374&q=80',
    categoryId: 'cat-7', // Books & Media
    storeId: 'store-1', // TechHub (assuming they also sell books)
    featured: true,
    inStock: true,
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z'
  }
];
