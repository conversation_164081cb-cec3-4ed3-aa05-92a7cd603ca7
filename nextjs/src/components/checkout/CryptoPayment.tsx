import React, { useState } from 'react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle, Copy, CheckCircle } from 'lucide-react';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { formatCurrency } from '@/lib/utils';

interface CryptoPaymentProps {
  amount: number;
  currency: string;
  onProceed: () => void;
}

// This would be fetched from your backend in a real implementation
const cryptoOptions = [
  { 
    id: 'btc', 
    name: 'Bitcoin (BTC)', 
    address: '**********************************',
    qrCode: '/images/btc-qr-placeholder.png'
  },
  { 
    id: 'eth', 
    name: 'Ethereum (ETH)', 
    address: '******************************************',
    qrCode: '/images/eth-qr-placeholder.png'
  },
  { 
    id: 'usdt', 
    name: '<PERSON><PERSON> (USDT)', 
    address: 'TKrV3XpMgMEfG668QL7hdpvM4vwrxBnNwh',
    qrCode: '/images/usdt-qr-placeholder.png'
  }
];

const CryptoPayment: React.FC<CryptoPaymentProps> = ({ amount, currency, onProceed }) => {
  const [selectedCrypto, setSelectedCrypto] = useState(cryptoOptions[0]);
  const [copied, setCopied] = useState(false);
  
  // In a real implementation, you would convert the amount to the selected cryptocurrency
  const cryptoAmount = amount; // This would be converted based on current exchange rates

  const handleCopyAddress = () => {
    navigator.clipboard.writeText(selectedCrypto.address);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const handleSelectCrypto = (value: string) => {
    const selected = cryptoOptions.find(crypto => crypto.id === value);
    if (selected) {
      setSelectedCrypto(selected);
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <h2 className="text-xl font-semibold mb-4">Pay with Cryptocurrency</h2>
      
      <Alert className="mb-6">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Please send the equivalent of {formatCurrency(amount, currency)} in cryptocurrency to our wallet.
        </AlertDescription>
      </Alert>
      
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Select Cryptocurrency
        </label>
        <Select 
          value={selectedCrypto.id} 
          onValueChange={handleSelectCrypto}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select cryptocurrency" />
          </SelectTrigger>
          <SelectContent>
            {cryptoOptions.map(crypto => (
              <SelectItem key={crypto.id} value={crypto.id}>
                {crypto.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      
      <div className="flex flex-col items-center">
        <div className="mb-6 p-4 border border-gray-200 rounded-lg">
          {/* This is a placeholder. In production, use your actual crypto QR code */}
          <div className="relative w-64 h-64 mx-auto bg-gray-100 flex items-center justify-center">
            <p className="text-gray-500 text-sm">{selectedCrypto.name} QR Code Placeholder</p>
            {/* Uncomment when you have the actual QR code image */}
            {/* <Image
              src={selectedCrypto.qrCode}
              alt={`${selectedCrypto.name} QR Code`}
              fill
              className="object-contain"
            /> */}
          </div>
        </div>
        
        <div className="w-full max-w-md mb-6">
          <p className="text-center font-medium mb-2">Wallet Address:</p>
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
            <span className="font-mono text-xs sm:text-sm truncate max-w-[200px] sm:max-w-[300px]">
              {selectedCrypto.address}
            </span>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={handleCopyAddress}
              className="flex items-center gap-1 flex-shrink-0"
            >
              {copied ? (
                <>
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-green-500">Copied</span>
                </>
              ) : (
                <>
                  <Copy className="h-4 w-4" />
                  <span>Copy</span>
                </>
              )}
            </Button>
          </div>
        </div>
        
        <div className="w-full max-w-md space-y-4">
          <div className="p-4 bg-orange-50 rounded-md">
            <h3 className="font-medium text-orange-800 mb-2">Payment Instructions:</h3>
            <ol className="list-decimal pl-5 text-orange-700 space-y-1">
              <li>Open your cryptocurrency wallet</li>
              <li>Scan the QR code above or copy the wallet address</li>
              <li>Send the equivalent of {formatCurrency(amount, currency)}</li>
              <li>After payment, click "I've Paid" below</li>
              <li>You'll need to provide your transaction ID/hash</li>
            </ol>
          </div>
          
          <Button 
            onClick={onProceed}
            className="w-full bg-primary-600 hover:bg-primary-700"
          >
            I've Paid
          </Button>
          
          <p className="text-center text-sm text-gray-500">
            Please only click "I've Paid" after you have completed the payment.
          </p>
        </div>
      </div>
    </div>
  );
};

export default CryptoPayment;
