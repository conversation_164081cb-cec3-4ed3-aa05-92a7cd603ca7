"use client";

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { ArrowRight, Clock } from 'lucide-react';
import { Product } from '@/lib/types/ecommerce';
import { EcommerceClientService } from '@/lib/services/ecommerce-client';
import { formatCurrency, calculateDiscountPercentage } from '@/lib/utils/format';

interface DealsProps {
  title?: string;
  subtitle?: string;
  viewAllLink?: string;
  initialProducts?: Product[];
  limit?: number;
}

export default function Deals({
  title = "Limited Time Deals",
  subtitle = "Hurry up! These deals won't last long.",
  viewAllLink = "/deals",
  initialProducts,
  limit = 3
}: DealsProps) {
  const [products, setProducts] = useState<Product[]>(initialProducts || []);
  const [loading, setLoading] = useState(!initialProducts);

  useEffect(() => {
    if (!initialProducts) {
      const fetchProducts = async () => {
        try {
          // Get products with discounts (salePrice exists)
          const allProducts = await EcommerceClientService.getProducts({ limit: 20 });
          const discountedProducts = allProducts
            .filter(product => (product.salePrice || product.compare_at_price))
            .sort((a, b) => {
              const discountA = a.salePrice ? (a.price - a.salePrice) : (a.compare_at_price ? (a.price - a.compare_at_price) : 0);
              const discountB = b.salePrice ? (b.price - b.salePrice) : (b.compare_at_price ? (b.price - b.compare_at_price) : 0);
              return discountB - discountA;
            })
            .slice(0, limit);

          setProducts(discountedProducts);
        } catch (error) {
          console.error('Error fetching deals:', error);
        } finally {
          setLoading(false);
        }
      };

      fetchProducts();
    }
  }, [initialProducts, limit]);

  // Calculate remaining time (just for display purposes)
  const getRandomHours = () => Math.floor(Math.random() * 24) + 1;
  const getRandomMinutes = () => Math.floor(Math.random() * 60);

  return (
    <section className="bg-primary-50 py-12 sm:py-16">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-2xl font-bold tracking-tight text-gray-900 sm:text-3xl">
            {title}
          </h2>
          <p className="mx-auto mt-3 max-w-2xl text-lg text-gray-600">
            {subtitle}
          </p>
        </div>

        <div className="mt-10">
          {loading ? (
            <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
              {Array.from({ length: limit }).map((_, index) => (
                <div key={index} className="h-96 animate-pulse rounded-lg bg-gray-200"></div>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
              {products.map((product) => {
                const salePrice = product.salePrice || product.compare_at_price;
                const discountPercentage = salePrice
                  ? calculateDiscountPercentage(product.price, salePrice)
                  : 0;

                return (
                  <Link
                    key={product.id}
                    href={`/products/${product.slug || product.id}`}
                    className="group overflow-hidden rounded-lg bg-white shadow-md transition-all hover:shadow-lg"
                  >
                    <div className="relative h-64 overflow-hidden">
                      {(product.image_url || product.imageUrl) ? (
                        <Image
                          src={product.image_url || product.imageUrl || ''}
                          alt={product.name}
                          fill
                          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                          className="object-cover object-center transition-transform duration-300 group-hover:scale-105"
                        />
                      ) : (
                        <div className="flex h-full items-center justify-center bg-gray-200">
                          <span className="text-gray-400">No image</span>
                        </div>
                      )}

                      {/* Discount badge */}
                      <div className="absolute left-3 top-3 rounded-full bg-primary-600 px-3 py-1.5 text-sm font-bold text-white">
                        {discountPercentage}% OFF
                      </div>
                    </div>

                    <div className="p-6">
                      <h3 className="text-xl font-bold text-gray-900">{product.name}</h3>

                      <div className="mt-2 flex items-baseline">
                        <span className="text-2xl font-bold text-primary-600">
                          {formatCurrency(salePrice || product.price)}
                        </span>
                        {salePrice && (
                          <span className="ml-2 text-lg text-gray-500 line-through">
                            {formatCurrency(product.price)}
                          </span>
                        )}
                      </div>

                      <div className="mt-4 flex items-center text-sm text-gray-600">
                        <Clock className="mr-1.5 h-4 w-4 text-primary-500" />
                        <span>
                          Ends in {getRandomHours()}h {getRandomMinutes()}m
                        </span>
                      </div>
                    </div>
                  </Link>
                );
              })}
            </div>
          )}

          {viewAllLink && (
            <div className="mt-10 text-center">
              <Link
                href={viewAllLink}
                className="inline-flex items-center rounded-lg border border-primary-600 px-6 py-3 text-base font-medium text-primary-600 shadow-sm transition-colors hover:bg-primary-50"
              >
                View all deals
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </div>
          )}
        </div>
      </div>
    </section>
  );
}
