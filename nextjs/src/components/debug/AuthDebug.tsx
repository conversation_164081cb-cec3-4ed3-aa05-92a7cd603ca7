"use client";
import React, { useState, useEffect } from 'react';
import { createSPASassClient } from '@/lib/supabase/client';

export default function AuthDebug() {
  const [debugInfo, setDebugInfo] = useState<any>({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkEverything = async () => {
      try {
        const supabase = await createSPASassClient();
        const client = supabase.getSupabaseClient();
        
        // Check auth user
        const { data: { user }, error: userError } = await client.auth.getUser();
        
        let profile = null;
        let profileError = null;
        
        if (user) {
          // Try to get profile
          const { data: profileData, error: profError } = await client
            .from('profiles')
            .select('*')
            .eq('id', user.id)
            .single();
          
          profile = profileData;
          profileError = profError;
        }
        
        // Check RLS policies
        const { data: stores, error: storesError } = await client
          .from('stores')
          .select('*')
          .limit(1);
          
        const { data: products, error: productsError } = await client
          .from('products')
          .select('*')
          .limit(1);
          
        const { data: categories, error: categoriesError } = await client
          .from('categories')
          .select('*')
          .limit(1);
        
        setDebugInfo({
          user: user ? { id: user.id, email: user.email } : null,
          userError,
          profile,
          profileError,
          stores: stores ? stores.length : 0,
          storesError,
          products: products ? products.length : 0,
          productsError,
          categories: categories ? categories.length : 0,
          categoriesError,
          timestamp: new Date().toISOString()
        });
        
      } catch (error) {
        setDebugInfo({ 
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString()
        });
      } finally {
        setLoading(false);
      }
    };
    
    checkEverything();
  }, []);
  
  if (loading) {
    return <div className="p-4 bg-yellow-100 text-yellow-800">Loading debug info...</div>;
  }
  
  return (
    <div className="fixed bottom-4 right-4 max-w-md p-4 bg-gray-900 text-white text-xs rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto">
      <h3 className="font-bold mb-2">🐛 Debug Info</h3>
      <pre className="whitespace-pre-wrap">
        {JSON.stringify(debugInfo, null, 2)}
      </pre>
    </div>
  );
}
