-- Fix notifications and order_store_items issues
-- Run this script in your Supabase SQL editor

BEGIN;

-- 1. Add role column to profiles table if it doesn't exist
ALTER TABLE profiles
ADD COLUMN IF NOT EXISTS role TEXT DEFAULT 'user';

-- Add check constraint for valid roles
ALTER TABLE profiles
DROP CONSTRAINT IF EXISTS profiles_role_check;

ALTER TABLE profiles
ADD CONSTRAINT profiles_role_check
CHECK (role IN ('user', 'store_owner', 'admin'));

-- 2. Set <EMAIL> as admin
UPDATE profiles
SET role = 'admin'
WHERE email = '<EMAIL>';

-- 3. Set <EMAIL> as store_owner
UPDATE profiles
SET role = 'store_owner'
WHERE email = '<EMAIL>';

-- 4. Debug: Check current user and store data
-- First, let's see what users exist
SELECT 'Current users:' as debug_info;
SELECT id, email, role, created_at FROM profiles OR<PERSON>R BY created_at;

-- Check stores and their owners
SELECT 'Current stores:' as debug_info;
SELECT s.id, s.name, s.owner_id, p.email as owner_email
FROM stores s
LEFT JOIN profiles p ON s.owner_id = p.id
ORDER BY s.created_at;

-- <NAME_EMAIL> has any stores
SELECT '<NAME_EMAIL>:' as debug_info;
SELECT s.* FROM stores s
JOIN profiles p ON s.owner_id = p.id
WHERE p.email = '<EMAIL>';

-- Check orders and order items
SELECT 'Recent orders:' as debug_info;
SELECT o.id, o.user_id, o.status, o.total, o.created_at,
       COUNT(oi.id) as item_count
FROM orders o
LEFT JOIN order_items oi ON o.id = oi.order_id
GROUP BY o.id, o.user_id, o.status, o.total, o.created_at
ORDER BY o.created_at DESC
LIMIT 10;

-- Check order items by store
SELECT 'Order items by store:' as debug_info;
SELECT oi.store_id, s.name as store_name, COUNT(*) as order_count
FROM order_items oi
JOIN stores s ON oi.store_id = s.id
GROUP BY oi.store_id, s.name
ORDER BY order_count DESC;

-- 5. Fix store <NAME_EMAIL>
-- If <EMAIL> doesn't have stores, assign them to existing stores or create one
DO $$
DECLARE
    find_user_id UUID;
    store_count INTEGER;
BEGIN
    -- Get <EMAIL> user ID
    SELECT id INTO find_user_id FROM profiles WHERE email = '<EMAIL>';

    IF find_user_id IS NOT NULL THEN
        -- Check how <NAME_EMAIL> owns
        SELECT COUNT(*) INTO store_count FROM stores WHERE owner_id = find_user_id;

        IF store_count = 0 THEN
            -- If no stores, assign them to the first available store or create one
            IF EXISTS (SELECT 1 FROM stores LIMIT 1) THEN
                -- Assign <EMAIL> to the first store
                UPDATE stores
                SET owner_id = find_user_id
                WHERE id = (SELECT id FROM stores ORDER BY created_at LIMIT 1);

                RAISE NOTICE 'Assigned <EMAIL> to existing store';
            ELSE
                -- Create a new <NAME_EMAIL>
                INSERT INTO stores (name, slug, description, owner_id, status, featured)
                VALUES (
                    'Test Store',
                    'test-store',
                    'A test store for development',
                    find_user_id,
                    'active',
                    true
                );

                RAISE NOTICE 'Created new <NAME_EMAIL>';
            END IF;
        ELSE
            RAISE NOTICE '<EMAIL> already owns % store(s)', store_count;
        END IF;
    ELSE
        RAISE NOTICE '<EMAIL> user not found';
    END IF;
END $$;

-- 6. Ensure order_store_items table exists with proper structure
CREATE TABLE IF NOT EXISTS order_store_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
  store_id UUID NOT NULL REFERENCES stores(id),
  total_amount DECIMAL(10, 2) NOT NULL,
  currency TEXT NOT NULL DEFAULT 'GMD',
  status TEXT NOT NULL DEFAULT 'pending',
  rejection_reason TEXT,
  rejection_notes TEXT,
  accepted_at TIMESTAMP WITH TIME ZONE,
  rejected_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(order_id, store_id)
);

-- Add check constraint for order_store_items status
ALTER TABLE order_store_items
DROP CONSTRAINT IF EXISTS order_store_items_status_check;

ALTER TABLE order_store_items
ADD CONSTRAINT order_store_items_status_check
CHECK (status IN ('pending', 'under_review', 'awaiting_store_confirmation', 'accepted', 'rejected', 'completed'));

-- 7. Create indexes for better performance
CREATE INDEX IF NOT EXISTS order_store_items_order_id_idx ON order_store_items(order_id);
CREATE INDEX IF NOT EXISTS order_store_items_store_id_idx ON order_store_items(store_id);
CREATE INDEX IF NOT EXISTS order_store_items_status_idx ON order_store_items(status);

-- 8. Enable RLS on order_store_items
ALTER TABLE order_store_items ENABLE ROW LEVEL SECURITY;

-- 9. Drop existing policies to avoid conflicts
DROP POLICY IF EXISTS "authenticated_users_can_insert_order_store_items" ON order_store_items;
DROP POLICY IF EXISTS "users_can_view_their_order_store_items" ON order_store_items;
DROP POLICY IF EXISTS "store_owners_can_update_order_store_items" ON order_store_items;
DROP POLICY IF EXISTS "Store owners can view their store order items" ON order_store_items;
DROP POLICY IF EXISTS "Store owners can update their store order items" ON order_store_items;
DROP POLICY IF EXISTS "Admins can view all order store items" ON order_store_items;

-- 10. Create comprehensive RLS policies for order_store_items

-- Allow authenticated users to insert order_store_items (needed for checkout)
CREATE POLICY "order_store_items_insert_policy" ON order_store_items
  FOR INSERT TO authenticated
  WITH CHECK (true);

-- Allow users to view order_store_items for their own orders, store owners for their stores, and admins for all
CREATE POLICY "order_store_items_select_policy" ON order_store_items
  FOR SELECT TO authenticated
  USING (
    -- Users can see their own order items
    EXISTS (
      SELECT 1 FROM orders
      WHERE id = order_store_items.order_id AND user_id = auth.uid()
    ) OR
    -- Store owners can see items for their stores
    EXISTS (
      SELECT 1 FROM stores
      WHERE id = order_store_items.store_id AND owner_id = auth.uid()
    ) OR
    -- Admins can see all
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'admin'
    ) OR
    -- Fallback for admin email
    auth.email() = '<EMAIL>'
  );

-- Allow store owners and admins to update order_store_items
CREATE POLICY "order_store_items_update_policy" ON order_store_items
  FOR UPDATE TO authenticated
  USING (
    -- Store owners can update items for their stores
    EXISTS (
      SELECT 1 FROM stores
      WHERE id = order_store_items.store_id AND owner_id = auth.uid()
    ) OR
    -- Admins can update all
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'admin'
    ) OR
    -- Fallback for admin email
    auth.email() = '<EMAIL>'
  );

-- 11. Grant necessary permissions
GRANT ALL ON order_store_items TO authenticated;
GRANT ALL ON order_store_items TO anon;

-- 12. Fix notifications table constraints if needed
ALTER TABLE notifications
DROP CONSTRAINT IF EXISTS notifications_type_check;

ALTER TABLE notifications
ADD CONSTRAINT notifications_type_check
CHECK (type IN ('order', 'payment', 'order_review', 'store_confirmation', 'payment_confirmed', 'general'));

-- 13. Ensure notifications RLS policies are correct
DROP POLICY IF EXISTS "users_view_own_notifications_simple" ON notifications;
DROP POLICY IF EXISTS "users_update_own_notifications_simple" ON notifications;
DROP POLICY IF EXISTS "system_insert_notifications_simple" ON notifications;

CREATE POLICY "notifications_select_policy" ON notifications
  FOR SELECT TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "notifications_update_policy" ON notifications
  FOR UPDATE TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "notifications_insert_policy" ON notifications
  FOR INSERT TO authenticated
  WITH CHECK (true);

COMMIT;
